package controllers

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	"sort"
	"strings"
	"sync"

	"github.com/astaxie/beego"
	grafanasdk "github.com/grafana-tools/sdk"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/cprom"
	cpromv1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/cprom/apis/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/compatible"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errorcode"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/middleware"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/configuration"
)

const (
	tagProd                           = "prod"
	tagCCE                            = "CCE"
	maxCPromInstancesPerRegionPerAcct = "100"

	controlComponentPanel = "控制面组件监控"

	folderSortPrefix = "folder"

	sep = "-"
)

type metaResponse struct {
	Data      []boardInfo `json:"data"`
	RequestID string      `json:"requestID"`
	Code      int         `json:"code"`
}

type boardInfo struct {
	SortPosition string                  `json:"sortPosition"`
	FolderTitle  string                  `json:"folderTitle"`
	Dashboards   []grafanasdk.FoundBoard `json:"dashboards"`
	Vars         map[string]string       `json:"vars"`
}

type grafanaMetaResponse struct {
	Data []struct {
		FolderTitle string `json:"folderTitle"`
	} `json:"data"`
	Code      int    `json:"code"`
	RequestID string `json:"requestID"`
}

// responseRecorder 用于捕获代理响应的状态码和响应体
type responseRecorder struct {
	http.ResponseWriter
	statusCode int
	body       []byte
}

func (r *responseRecorder) WriteHeader(statusCode int) {
	r.statusCode = statusCode
	r.ResponseWriter.WriteHeader(statusCode)
}

func (r *responseRecorder) Write(data []byte) (int, error) {
	r.body = append(r.body, data...)
	return r.ResponseWriter.Write(data)
}

// ConsoleGrafana sync controller
type ConsoleGrafana struct {
	BaseController

	clusterID string

	initConfig     sync.Once
	initProxy      sync.Once
	initGrafanaCli sync.Once

	config            *configuration.Config
	authMiddlw        middleware.Interface
	clientSet         *clientset.ClientSet
	proxy             *httputil.ReverseProxy
	englishProxy      *httputil.ReverseProxy
	grafanaCli        *grafanasdk.Client
	englishGrafanaCli *grafanasdk.Client
}

func (c *ConsoleGrafana) getGrafanaCli(ctx context.Context) (*grafanasdk.Client, *grafanasdk.Client) {
	c.initGrafanaCli.Do(func() {
		cfg := c.getConfig(ctx)
		cl, err := grafanasdk.NewClient(cfg.ConsoleGrafanaAddress,
			fmt.Sprintf("%s:%s", cfg.ConsoleGrafanaUser, cfg.ConsoleGrafanaPassword),
			grafanasdk.DefaultHTTPClient,
		)
		if err != nil {
			logger.Errorf(ctx, "new grafana client err: %v", err)
			panic(err)
		}

		cl2, err := grafanasdk.NewClient(cfg.EnglishConsoleGrafanaAddress,
			fmt.Sprintf("%s:%s", cfg.EnglishConsoleGrafanaUser, cfg.EnglishConsoleGrafanaPassword),
			grafanasdk.DefaultHTTPClient,
		)
		if err != nil {
			logger.Errorf(ctx, "new grafana client err: %v", err)
			panic(err)
		}

		c.grafanaCli = cl
		c.englishGrafanaCli = cl2
	})
	return c.grafanaCli, c.englishGrafanaCli
}

func (c *ConsoleGrafana) getConfig(ctx context.Context) *configuration.Config {
	c.initConfig.Do(func() {
		configFile := beego.AppConfig.String(configuration.ServiceConfigFile)

		pluginConfigFile := beego.AppConfig.String(configuration.PluginConfigFile)

		config, err := configuration.NewConfig(ctx, configFile, pluginConfigFile)
		if err != nil {
			logger.Errorf(ctx, "new configuration.NewConfig failed: %v", err)
			panic(err)
		}

		c.config = config
	})

	return c.config
}

func (c *ConsoleGrafana) getProxy(ctx context.Context) (*httputil.ReverseProxy, *httputil.ReverseProxy) {
	c.initProxy.Do(func() {
		cfg := c.getConfig(ctx)
		p, err := newProxy(cfg.ConsoleGrafanaAddress)
		if err != nil {
			logger.Errorf(ctx, "new grafana proxy for: %v err: %v", cfg.ConsoleGrafanaAddress, err)
			panic(err)
		}

		logger.Infof(ctx, "new grafana proxy for: %v", cfg.ConsoleGrafanaAddress)

		p2, err := newProxy(cfg.EnglishConsoleGrafanaAddress)
		if err != nil {
			logger.Errorf(ctx, "new grafana proxy for: %v err: %v", cfg.EnglishConsoleGrafanaAddress, err)
			panic(err)
		}

		logger.Infof(ctx, "new english grafana proxy for: %v", cfg.EnglishConsoleGrafanaAddress)

		c.proxy = p
		c.englishProxy = p2
	})

	return c.proxy, c.englishProxy
}

func newProxy(targetHost string) (*httputil.ReverseProxy, error) {
	url, err := url.Parse(targetHost)
	if err != nil {
		return nil, err
	}

	return httputil.NewSingleHostReverseProxy(url), nil
}

func (c *ConsoleGrafana) getBindingMonitorInstance(ctx context.Context, clusterID string) (*cpromv1.MonitorInstance, error) {
	listInstanceResp, err := c.BaseController.clients.CPromClient.ListInstance(ctx, cprom.ListInstanceRequest{
		// 理论上一个用户一个地域 mi 数量不超过 100
		"pageSize": maxCPromInstancesPerRegionPerAcct,
		"pageNo":   "1",
	}, c.BaseController.clients.STSClient.NewSignOption(ctx, c.accountID))

	if err != nil {
		logger.Errorf(ctx, "cprom client list instances err: %v", err)
		return nil, err
	}

	logger.Infof(ctx, "account: %v found: %v mi", c.accountID, listInstanceResp.Result.TotalCount)

	for _, mi := range listInstanceResp.Result.Items {
		listAgentResp, err := c.BaseController.clients.CPromClient.ListAgent(ctx, cprom.ListAgentRequest{
			"instanceID":  mi.Spec.InstanceID,
			"keywordType": "agentName",
			"keyword":     clusterID,
		}, c.BaseController.clients.STSClient.NewSignOption(ctx, c.accountID))

		if err != nil {
			logger.Errorf(ctx, "cprom client list agent err: %v", err)
			continue
		}

		if len(listAgentResp.Result.Items) == 0 {
			logger.Warnf(ctx, "list agent got 0 item")
			continue
		}

		return &mi, nil
	}

	return nil, fmt.Errorf("not found binding monitor instance for clusterID: %v", clusterID)
}

func (c *ConsoleGrafana) AuthProxy() {
	ctx := c.ctx

	clusterID := c.Ctx.Input.Query("clusterID")

	r := c.Ctx.Request
	logger.Infof(ctx, "grafana auth proxy url: %v", r.URL.String())

	// 带有 clusterID query 参数需要设置一些参数
	// 避免 恶意查询其他用户的数据
	if clusterID != "" {
		mi, err := c.getBindingMonitorInstance(ctx, clusterID)
		if err != nil {
			logger.Errorf(ctx, "get binding monitor instance err: %v", err)

			c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByAdmin, "err: %v", err)
			return
		}

		query := r.URL.Query()

		// 添加一些必要的参数变量
		query.Add("var-clusterID", clusterID)
		query.Add("var-datasource", mi.Spec.InstanceID)

		r.URL.RawQuery = query.Encode()

		logger.Infof(ctx, "url query: %v", r.URL.Query())
	}

	// clusterID == "" 目前代理的是 grafana "css/js/api接口"

	// 这个 Header 会影响 grafana auth 逻辑，需要删掉
	r.Header.Del("Authorization")
	r.Header.Set("X-WEBAUTH-USER", c.userID)

	locale := c.GetLocale()
	logger.Infof(ctx, "locale: %v", locale)

	// 根据 locale 获取不同的 proxy
	chineseProxy, englishProxy := c.getProxy(ctx)
	var proxy = chineseProxy
	if locale == LocaleEnUS {
		proxy = englishProxy
	}

	// 创建一个自定义的 ResponseWriter 来捕获响应
	recorder := &responseRecorder{
		ResponseWriter: c.Ctx.ResponseWriter,
		statusCode:     200,
		body:           make([]byte, 0),
	}

	proxy.ServeHTTP(recorder, r)

	if recorder.statusCode/100 != 2 {
		logger.Errorf(ctx, "proxy status: %v, request_host: %s, request_url: %s, response body: %s, header: %v",
			recorder.statusCode, r.Host, r.URL.String(), string(recorder.body), r.Header)

		// 如果第一个代理失败，尝试使用中文代理
		chineseRecorder := &responseRecorder{
			ResponseWriter: c.Ctx.ResponseWriter,
			statusCode:     200,
			body:           make([]byte, 0),
		}
		chineseProxy.ServeHTTP(chineseRecorder, r)

		if chineseRecorder.statusCode/100 != 2 {
			logger.Errorf(ctx, "chinese proxy also failed, status: %v, response body: %s",
				chineseRecorder.statusCode, string(chineseRecorder.body))
		}
	}
}

// Prepare run before HandlerFunc
func (c *ConsoleGrafana) Prepare() {
	c.BaseController.Prepare()
	ctx := c.ctx

	clusterID := c.Ctx.Input.Query("clusterID")

	if clusterID == "" {
		clusterID = c.Ctx.Input.Query("var-clusterID")
	}

	if clusterID != "" {
		client, err := compatible.NewClientByClients(ctx, c.accountID, clusterID, c.BaseController.clientSet)
		if err != nil {
			logger.Errorf(ctx, "NewClientByClients failed: %s", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
		}

		if err := client.CheckClusterAccount(ctx, c.accountID, clusterID); err != nil {
			logger.Errorf(ctx, "check cluster account failed: %s", err)
			c.errorHandlerV2(errorcode.NewInvalidAuth(), errorcode.LevelByAdmin, err.Error())
		}
	}
}

func (c *ConsoleGrafana) getMasterType(ctx context.Context, clusterID string) (ccetypes.MasterType, error) {
	cluster, err := c.clusterCompatible(ctx, clusterID, false)
	if err != nil {
		logger.Errorf(ctx, "get cluster failed, err: %v", err)
		return ccetypes.MasterTypeManagedPro, err
	}
	return cluster.Spec.MasterConfig.MasterType, nil
}

// GetMeta 获取 grafana 一些元数据
func (c *ConsoleGrafana) GetMeta() {
	ctx := c.ctx

	clusterID := c.Ctx.Input.Query("clusterID")
	tag := c.Ctx.Input.Query("tag")
	if tag == "" {
		tag = tagCCE
	}

	masterType, err := c.getMasterType(ctx, clusterID)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByAdmin, "err: %v", err)
		return
	}

	mi, err := c.getBindingMonitorInstance(ctx, clusterID)
	if err != nil {
		logger.Errorf(ctx, "get binding monitor instance err: %v", err)

		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByAdmin, "err: %v", err)
		return
	}

	chineseCli, englishCli := c.getGrafanaCli(ctx)
	var cli = chineseCli
	if c.GetLocale() == LocaleEnUS {
		cli = englishCli
	}
	folders, err := cli.GetAllFolders(ctx)
	if err != nil {
		logger.Errorf(ctx, "grafana client list all folders err: %v", err)
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByAdmin, "list grafana folders err: %v", err)
		return
	}

	resp := metaResponse{}

	for _, f := range folders {

		// 非托管集群不展示控制面组件监控面板
		if masterType != ccetypes.MasterTypeManaged &&
			masterType != ccetypes.MasterTypeManagedPro &&
			masterType != ccetypes.MasterTypeContainerizedManaged &&
			masterType != ccetypes.MasterTypeManagedOld &&
			strings.Contains(f.Title, controlComponentPanel) {
			continue
		}

		info := boardInfo{FolderTitle: f.Title}
		// 从 grafana 中的 dashboard 有 `prod` tag 才会返回给前端,tag 分业务线，默认为 CCE
		searchParmas := []grafanasdk.SearchParam{
			grafanasdk.SearchFolderID(f.ID),
			grafanasdk.SearchTag(tagProd),
			grafanasdk.SearchTag(tag),
		}

		boards, err := cli.Search(ctx, searchParmas...)
		if err != nil {
			logger.Errorf(ctx, "grafana client search err: %v", err)
			continue
		}

		if len(boards) == 0 {
			logger.Warnf(ctx, "found 0 boards")
			continue
		}

		info.Dashboards = boards
		sort.Slice(info.Dashboards, func(i, j int) bool {
			var tagI, tagJ string
			for _, item := range info.Dashboards[i].Tags {
				if strings.HasPrefix(item, "board") {
					tagI = item
					break
				}
			}

			for _, item := range info.Dashboards[j].Tags {
				if strings.HasPrefix(item, "board") {
					tagJ = item
					break
				}
			}
			return strings.Compare(tagI, tagJ) < 0
		})
		info.Vars = map[string]string{
			"var-datasource": mi.Spec.InstanceID,
			"var-clusterID":  clusterID,
		}
		var folderPosition string
		// 使用dashboard的tag做排序 使用前缀为folder的tag作为排序项
		for _, tag := range boards[0].Tags {
			if strings.HasPrefix(tag, folderSortPrefix) {
				folderPosition = tag
				break
			}
		}
		info.SortPosition = folderPosition

		// 转换语言
		if c.GetLocale() == LocaleEnUS {
			info.FolderTitle = GetGrafanaEnUSAttr(info.FolderTitle, LocaleEnUS)
			for i := range info.Dashboards {
				info.Dashboards[i].Title = GetGrafanaEnUSAttr(info.Dashboards[i].Title, LocaleEnUS)
				info.Dashboards[i].FolderTitle = GetGrafanaEnUSAttr(info.Dashboards[i].FolderTitle, LocaleEnUS)
				// info.Dashboards[i].FolderURL = fmt.Sprintf("%s?locale=%s", info.Dashboards[i].FolderURL, LocaleEnUS)
			}
		}
		resp.Data = append(resp.Data, info)
	}
	sort.Slice(resp.Data, func(i, j int) bool {
		return strings.Compare(resp.Data[i].SortPosition, resp.Data[j].SortPosition) < 0
	})

	c.Data["json"] = resp

	c.ServeJSON()
}
