// nolint
package controllers

import "testing"

func TestGetGrafanaEnUSAttr(t *testing.T) {
	tests := []struct {
		name     string
		attr     string
		locale   Locale
		expected string
	}{
		{
			name:     "en-us locale with existing key",
			attr:     "集群监控",
			locale:   LocaleEnUS,
			expected: "Cluster",
		},
		{
			name:     "en-us locale with non-existing key",
			attr:     "不存在的key",
			locale:   LocaleEnUS,
			expected: "不存在的key",
		},
		{
			name:     "zh-cn locale with existing key",
			attr:     "集群监控",
			locale:   LocaleZhCN,
			expected: "集群监控",
		},
		{
			name:     "empty locale",
			attr:     "集群监控",
			locale:   "",
			expected: "集群监控",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetGrafanaEnUSAttr(tt.attr, tt.locale); got != tt.expected {
				t.<PERSON><PERSON><PERSON>("GetGrafanaEnUSAttr() = %v, want %v", got, tt.expected)
			}
		})
	}
}
