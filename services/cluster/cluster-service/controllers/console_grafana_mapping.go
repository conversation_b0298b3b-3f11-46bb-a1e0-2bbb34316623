package controllers

var (
	grafanaDashboardMap = map[string]string{
		// Folder titles
		"集群监控":    "Cluster",
		"控制面组件监控": "Control Plane Component",
		"节点监控":    "Node",
		"工作负载监控":  "Workload",
		"云原生AI监控": "Cloud Native AI",
		"网络监控":    "Network",
		"组件监控":    "Component",

		// 集群监控
		"集群概览":          "Cluster Overview",
		"集群Namespace监控": "Cluster Namespace",
	}
)

// 获取Grafana的英文字段
func GetGrafanaEnUSAttr(attr string, locale Locale) string {
	if locale == "en-us" {
		val, ok := grafanaDashboardMap[attr]
		if ok {
			return val
		}
	}
	return attr
}
