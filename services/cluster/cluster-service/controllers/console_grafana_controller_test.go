package controllers

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"sync"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/astaxie/beego"
	beegocontext "github.com/astaxie/beego/context"
	beegoctx "github.com/astaxie/beego/context"
	"github.com/golang/mock/gomock"
	grafanasdk "github.com/grafana-tools/sdk"
	"github.com/stretchr/testify/assert"

	"net/http/httputil"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/cprom"
	cpromv1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/cprom/apis/v1"
	cprommock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/cprom/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/compatible"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errorcode"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/middleware"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts"
	stsmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts/mock"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/configuration"
)

func Test_AuthProxy(t *testing.T) {
	var (
		ast         = assert.New(t)
		errHandler  = errorcode.NewMockErrorHandler()
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		c           = &ConsoleGrafana{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				config:     &configuration.Config{},
				clients: &clients.Clients{
					CPromClient: &cprom.Client{},
					STSClient:   &sts.Client{},
				},
				clientSet: &clientset.ClientSet{},
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							URL:    &url.URL{},
							Header: http.Header{},
						},
						Input: &beegocontext.BeegoInput{
							Context: &beegoctx.Context{
								Request: &http.Request{
									URL: &url.URL{
										RawQuery: "locale=en-us",
									},
								},
							},
						},
						ResponseWriter: &beegocontext.Response{},
					},
					Data: make(map[interface{}]interface{}),
				},
			},
		}
	)

	patch := gomonkey.ApplyFunc((*ConsoleGrafana).getConfig, func(_ *ConsoleGrafana, ctx context.Context) *configuration.Config {
		return &configuration.Config{
			ConsoleGrafanaAddress: "http://localhost:3000",
		}
	})
	defer patch.Reset()

	defer func() {
		recover()
	}()

	c.AuthProxy()
	ast.Nil(errHandler.ErrorCode)
}

func TestConsoleGrafana_getBindingMonitorInstance(t *testing.T) {
	mockController := gomock.NewController(t)
	ctx := context.Background()
	type fields struct {
		BaseController BaseController
		clusterID      string
		initConfig     sync.Once
		initProxy      sync.Once
		initGrafanaCli sync.Once
		config         *configuration.Config
		authMiddlw     middleware.Interface
		clientSet      *clientset.ClientSet
		proxy          *httputil.ReverseProxy
		grafanaCli     *grafanasdk.Client
	}
	type args struct {
		ctx       context.Context
		clusterID string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *cpromv1.MonitorInstance
		wantErr bool
	}{
		// TODO: Add test cases.
		{

			name: "Test case 1",
			fields: fields{
				// 提供合适的字段初始化
				BaseController: BaseController{
					Controller: beego.Controller{},
					ctx:        ctx,
					clients: &clients.Clients{
						CPromClient: func() cprom.Interface {
							cpromMockClient := cprommock.NewMockInterface(mockController)
							cpromMockClient.EXPECT().ListInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(&cprom.ListInstanceResponse{
								Result: struct {
									KeywordType string                    `json:"keywordType,omitempty"`
									Keyword     string                    `json:"keyword,omitempty"`
									OrderBy     string                    `json:"orderBy,omitempty"`
									Order       string                    `json:"order,omitempty"`
									PageNo      int                       `json:"pageNo"`
									PageSize    int                       `json:"pageSize"`
									TotalCount  int                       `json:"totalCount"`
									Items       []cpromv1.MonitorInstance `json:"items"`
								}{
									KeywordType: "SomeKeywordType",
									Keyword:     "SomeKeyword",
									OrderBy:     "SomeOrderBy",
									Order:       "SomeOrder",
									PageNo:      1,
									PageSize:    10,
									TotalCount:  100,
									Items: []cpromv1.MonitorInstance{
										{
											Spec: cpromv1.MonitorInstanceSpec{
												InstanceID: "instanceID",
											},
										},
									},
								},
							}, nil).AnyTimes()

							cpromMockClient.EXPECT().ListAgent(gomock.Any(), gomock.Any(), gomock.Any()).Return(&cprom.ListAgentResponse{
								Result: struct {
									KeywordType string                 `json:"keywordType,omitempty"`
									Keyword     string                 `json:"keyword,omitempty"`
									OrderBy     string                 `json:"orderBy,omitempty"`
									Order       string                 `json:"order,omitempty"`
									PageNo      int                    `json:"pageNo"`
									PageSize    int                    `json:"pageSize"`
									TotalCount  int                    `json:"totalCount"`
									Items       []cpromv1.MonitorAgent `json:"items"`
								}{
									KeywordType: "SomeKeywordType",
									Keyword:     "SomeKeyword",
									OrderBy:     "SomeOrderBy",
									Order:       "SomeOrder",
									PageNo:      1,
									PageSize:    10,
									TotalCount:  100,
									Items:       []cpromv1.MonitorAgent{},
								},
							}, nil).AnyTimes()
							return cpromMockClient
						}(),
						STSClient: func() sts.Interface {
							stsMockClient := stsmock.NewMockInterface(mockController)
							stsMockClient.EXPECT().NewSignOption(ctx, "accountID").Return(nil).AnyTimes()
							return stsMockClient
						}(),
					},
					accountID: "accountID",
				},
				clusterID: "clusterID",
			},
			args: args{
				// 使用前面定义的输入参数
				ctx: ctx,
			},
			want: &cpromv1.MonitorInstance{
				// 使用前面定义的期望输出
			},
			wantErr: false, // 根据实际情况指定是否期望发生错误
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ConsoleGrafana{
				BaseController: tt.fields.BaseController,
				clusterID:      tt.fields.clusterID,
				initConfig:     tt.fields.initConfig,
				initProxy:      tt.fields.initProxy,
				initGrafanaCli: tt.fields.initGrafanaCli,
				config:         tt.fields.config,
				authMiddlw:     tt.fields.authMiddlw,
				clientSet:      tt.fields.clientSet,
				proxy:          tt.fields.proxy,
				grafanaCli:     tt.fields.grafanaCli,
			}
			_, err := c.getBindingMonitorInstance(tt.args.ctx, tt.args.clusterID)
			if err != nil {
				t.Logf(err.Error())
				return
			}

		})
	}
}

// TestConsoleGrafana_getGrafanaCli tests the getGrafanaCli method
func TestConsoleGrafana_getGrafanaCli(t *testing.T) {
	var (
		ast         = assert.New(t)
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
	)
	defer cancel()

	// Test successful client creation
	t.Run("successful client creation", func(t *testing.T) {
		c := &ConsoleGrafana{
			BaseController: BaseController{
				ctx: ctx,
			},
		}

		mockConfig := &configuration.Config{
			ConsoleGrafanaAddress:         "http://localhost:3000",
			ConsoleGrafanaUser:            "admin",
			ConsoleGrafanaPassword:        "password",
			EnglishConsoleGrafanaAddress:  "http://localhost:3001",
			EnglishConsoleGrafanaUser:     "admin",
			EnglishConsoleGrafanaPassword: "password",
		}

		// Mock getConfig method
		getConfigPatch := gomonkey.ApplyFunc((*ConsoleGrafana).getConfig, func(_ *ConsoleGrafana, ctx context.Context) *configuration.Config {
			return mockConfig
		})
		defer getConfigPatch.Reset()

		// Mock grafanasdk.NewClient to return successful clients
		newClientPatch := gomonkey.ApplyFunc(grafanasdk.NewClient, func(url, auth string, httpClient *http.Client) (*grafanasdk.Client, error) {
			return &grafanasdk.Client{}, nil
		})
		defer newClientPatch.Reset()

		chineseCli, englishCli := c.getGrafanaCli(ctx)
		ast.NotNil(chineseCli)
		ast.NotNil(englishCli)
		ast.Equal(c.grafanaCli, chineseCli)
		ast.Equal(c.englishGrafanaCli, englishCli)

		// Test that subsequent calls return the same clients (singleton pattern)
		chineseCli2, englishCli2 := c.getGrafanaCli(ctx)
		ast.Equal(chineseCli, chineseCli2)
		ast.Equal(englishCli, englishCli2)
	})

	// Test grafana client creation error (first client fails)
	t.Run("grafana client creation error", func(t *testing.T) {
		c := &ConsoleGrafana{
			BaseController: BaseController{
				ctx: ctx,
			},
		}

		mockConfig := &configuration.Config{
			ConsoleGrafanaAddress:         "http://localhost:3000",
			ConsoleGrafanaUser:            "admin",
			ConsoleGrafanaPassword:        "password",
			EnglishConsoleGrafanaAddress:  "http://localhost:3001",
			EnglishConsoleGrafanaUser:     "admin",
			EnglishConsoleGrafanaPassword: "password",
		}

		// Mock getConfig method
		getConfigPatch := gomonkey.ApplyFunc((*ConsoleGrafana).getConfig, func(_ *ConsoleGrafana, ctx context.Context) *configuration.Config {
			return mockConfig
		})
		defer getConfigPatch.Reset()

		// Mock grafanasdk.NewClient to return error on first call
		newClientPatch := gomonkey.ApplyFunc(grafanasdk.NewClient, func(url, auth string, httpClient *http.Client) (*grafanasdk.Client, error) {
			return nil, fmt.Errorf("invalid URL")
		})
		defer newClientPatch.Reset()

		ast.Panics(func() {
			c.getGrafanaCli(ctx)
		})
	})

	// Test english grafana client creation error (second client fails)
	t.Run("english grafana client creation error", func(t *testing.T) {
		c := &ConsoleGrafana{
			BaseController: BaseController{
				ctx: ctx,
			},
		}

		mockConfig := &configuration.Config{
			ConsoleGrafanaAddress:         "http://localhost:3000",
			ConsoleGrafanaUser:            "admin",
			ConsoleGrafanaPassword:        "password",
			EnglishConsoleGrafanaAddress:  "http://localhost:3001",
			EnglishConsoleGrafanaUser:     "admin",
			EnglishConsoleGrafanaPassword: "password",
		}

		// Mock getConfig method
		getConfigPatch := gomonkey.ApplyFunc((*ConsoleGrafana).getConfig, func(_ *ConsoleGrafana, ctx context.Context) *configuration.Config {
			return mockConfig
		})
		defer getConfigPatch.Reset()

		// Mock grafanasdk.NewClient to return success on first call, error on second call
		callCount := 0
		newClientPatch := gomonkey.ApplyFunc(grafanasdk.NewClient, func(url, auth string, httpClient *http.Client) (*grafanasdk.Client, error) {
			callCount++
			if callCount == 1 {
				return &grafanasdk.Client{}, nil // First call succeeds
			}
			return nil, fmt.Errorf("invalid URL for english client") // Second call fails
		})
		defer newClientPatch.Reset()

		ast.Panics(func() {
			c.getGrafanaCli(ctx)
		})
	})
}

// TestConsoleGrafana_getProxy tests the getProxy method
func TestConsoleGrafana_getProxy(t *testing.T) {
	var (
		ast         = assert.New(t)
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
	)
	defer cancel()

	tests := []struct {
		name               string
		mockConfig         *configuration.Config
		mockNewProxyError  error
		expectPanic        bool
		expectProxyCreated bool
	}{
		{
			name: "successful proxy creation",
			mockConfig: &configuration.Config{
				ConsoleGrafanaAddress:        "http://localhost:3000",
				EnglishConsoleGrafanaAddress: "http://localhost:3001",
			},
			mockNewProxyError:  nil,
			expectPanic:        false,
			expectProxyCreated: true,
		},
		{
			name: "invalid chinese grafana address",
			mockConfig: &configuration.Config{
				ConsoleGrafanaAddress:        "invalid-url",
				EnglishConsoleGrafanaAddress: "http://localhost:3001",
			},
			mockNewProxyError:  fmt.Errorf("invalid URL"),
			expectPanic:        true,
			expectProxyCreated: false,
		},
		{
			name: "invalid english grafana address",
			mockConfig: &configuration.Config{
				ConsoleGrafanaAddress:        "http://localhost:3000",
				EnglishConsoleGrafanaAddress: "invalid-url",
			},
			mockNewProxyError:  fmt.Errorf("invalid URL for english proxy"),
			expectPanic:        true,
			expectProxyCreated: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ConsoleGrafana{
				BaseController: BaseController{
					ctx: ctx,
				},
			}

			// Mock getConfig method
			getConfigPatch := gomonkey.ApplyFunc((*ConsoleGrafana).getConfig, func(_ *ConsoleGrafana, ctx context.Context) *configuration.Config {
				return tt.mockConfig
			})
			defer getConfigPatch.Reset()

			// Mock newProxy function
			callCount := 0
			newProxyPatch := gomonkey.ApplyFunc(newProxy, func(targetHost string) (*httputil.ReverseProxy, error) {
				callCount++
				if tt.mockNewProxyError != nil {
					// Return error for the first call (Chinese proxy) or second call (English proxy) based on test case
					if (tt.name == "invalid chinese grafana address" && callCount == 1) ||
						(tt.name == "invalid english grafana address" && callCount == 2) {
						return nil, tt.mockNewProxyError
					}
				}
				return &httputil.ReverseProxy{}, nil
			})
			defer newProxyPatch.Reset()

			if tt.expectPanic {
				ast.Panics(func() {
					c.getProxy(ctx)
				})
			} else {
				chineseProxy, englishProxy := c.getProxy(ctx)
				ast.NotNil(chineseProxy)
				ast.NotNil(englishProxy)
				ast.Equal(c.proxy, chineseProxy)
				ast.Equal(c.englishProxy, englishProxy)

				// Test that subsequent calls return the same proxies (singleton pattern)
				chineseProxy2, englishProxy2 := c.getProxy(ctx)
				ast.Equal(chineseProxy, chineseProxy2)
				ast.Equal(englishProxy, englishProxy2)
			}
		})
	}
}

// TestNewProxy tests the newProxy function
func TestNewProxy(t *testing.T) {
	var ast = assert.New(t)

	tests := []struct {
		name        string
		targetHost  string
		expectError bool
	}{
		{
			name:        "valid http URL",
			targetHost:  "http://localhost:3000",
			expectError: false,
		},
		{
			name:        "valid https URL",
			targetHost:  "https://grafana.example.com",
			expectError: false,
		},
		{
			name:        "valid URL with port",
			targetHost:  "http://grafana.example.com:3000",
			expectError: false,
		},
		{
			name:        "relative URL",
			targetHost:  "/path/to/resource",
			expectError: false, // url.Parse accepts relative URLs
		},
		{
			name:        "empty URL",
			targetHost:  "",
			expectError: false, // url.Parse accepts empty strings
		},
		{
			name:        "URL with invalid scheme",
			targetHost:  "ftp://example.com",
			expectError: false, // url.Parse doesn't validate scheme, so this will succeed
		},
		{
			name:        "URL with special characters",
			targetHost:  "http://example.com/path?query=value&other=test",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			proxy, err := newProxy(tt.targetHost)

			if tt.expectError {
				ast.Error(err)
				ast.Nil(proxy)
			} else {
				ast.NoError(err)
				ast.NotNil(proxy)
				ast.IsType(&httputil.ReverseProxy{}, proxy)
			}
		})
	}
}

// TestConsoleGrafana_GetMeta tests the GetMeta method of ConsoleGrafana controller
func TestConsoleGrafana_GetMeta(t *testing.T) {
	var (
		ast         = assert.New(t)
		errHandler  = errorcode.NewMockErrorHandler()
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		c           = &ConsoleGrafana{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				config:     &configuration.Config{},
				clients: &clients.Clients{
					CPromClient: &cprom.Client{},
					STSClient:   &sts.Client{},
				},
				clientSet: &clientset.ClientSet{},
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							URL: &url.URL{},
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[interface{}]interface{}),
				},
			},
		}
	)

	// Mock ServeJSON方法
	serveJSONPatch := gomonkey.ApplyFunc((*beego.Controller).ServeJSON, func(self *beego.Controller, encoding ...bool) {
	})
	defer serveJSONPatch.Reset()

	// Mock Query方法
	var clusterID string
	var tag string
	queryPatch := gomonkey.ApplyFunc((*beegocontext.BeegoInput).Query, func(_ *beegocontext.BeegoInput, key string) string {
		switch key {
		case "clusterID":
			return clusterID
		case "tag":
			return tag
		default:
			return ""
		}
	})
	defer queryPatch.Reset()

	// Mock getMasterType方法返回错误
	var masterTypeError error
	masterTypePatch := gomonkey.ApplyFunc((*ConsoleGrafana).getMasterType, func(_ *ConsoleGrafana, ctx context.Context, clusterID string) (ccetypes.MasterType, error) {
		return ccetypes.MasterTypeManaged, masterTypeError
	})
	defer masterTypePatch.Reset()

	// Mock getBindingMonitorInstance方法返回错误
	var bindingMonitorInstanceError error
	bindingMonitorInstancePatch := gomonkey.ApplyFunc((*ConsoleGrafana).getBindingMonitorInstance, func(_ *ConsoleGrafana, ctx context.Context, clusterID string) (*cpromv1.MonitorInstance, error) {
		return &cpromv1.MonitorInstance{
			Spec: cpromv1.MonitorInstanceSpec{
				InstanceID: "test-instance-id",
			},
		}, bindingMonitorInstanceError
	})
	defer bindingMonitorInstancePatch.Reset()

	// Mock getGrafanaCli方法
	grafanaCliPatch := gomonkey.ApplyFunc((*ConsoleGrafana).getGrafanaCli, func(_ *ConsoleGrafana, ctx context.Context) (*grafanasdk.Client, *grafanasdk.Client) {
		return &grafanasdk.Client{}, &grafanasdk.Client{}
	})
	defer grafanaCliPatch.Reset()

	// Mock GetLocale方法
	localePatch := gomonkey.ApplyMethod(&c.BaseController, "GetLocale", func(_ *BaseController) Locale {
		return LocaleEnUS
	})
	defer localePatch.Reset()

	// Mock grafana client GetAllFolders方法返回错误
	var getAllFoldersError error
	getAllFoldersPatch := gomonkey.ApplyFunc((*grafanasdk.Client).GetAllFolders, func(cli *grafanasdk.Client, ctx context.Context) ([]grafanasdk.Folder, error) {
		return []grafanasdk.Folder{
			{ID: 1, Title: "集群监控"},
		}, getAllFoldersError
	})
	defer getAllFoldersPatch.Reset()

	// Mock grafana client Search方法返回错误
	var searchError error
	searchPatch := gomonkey.ApplyFunc((*grafanasdk.Client).Search, func(cli *grafanasdk.Client, ctx context.Context, params ...grafanasdk.SearchParam) ([]grafanasdk.FoundBoard, error) {
		return []grafanasdk.FoundBoard{
			{
				ID:          1,
				Title:       "集群概览",
				FolderTitle: "集群监控",
				Tags:        []string{"prod", "CCE", "board1"},
			},
		}, searchError
	})
	defer searchPatch.Reset()

	// Mock NewSignOption方法
	newSignOptionPatch := gomonkey.ApplyMethodFunc(&sts.Client{}, "NewSignOption", func(cli *sts.Client, ctx context.Context, accountID string) *bce.SignOption {
		return &bce.SignOption{}
	})
	defer newSignOptionPatch.Reset()

	// Mock compatible.NewClientByClients
	newClientByClientsPatch := gomonkey.ApplyFunc(compatible.NewClientByClients, func(ctx context.Context, accountID string, clusterID string, clientSet *clientset.ClientSet) (compatible.Interface, error) {
		return nil, nil
	})
	defer newClientByClientsPatch.Reset()

	// 测试正常流程
	clusterID = "test-cluster"
	c.GetMeta()
	ast.Nil(errHandler.ErrorCode)

	// 测试getMasterType返回错误
	masterTypeError = fmt.Errorf("get master type failed")
	c.GetMeta()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInvalidParam())
	masterTypeError = nil

	// 测试getBindingMonitorInstance返回错误
	bindingMonitorInstanceError = fmt.Errorf("get binding monitor instance failed")
	c.GetMeta()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInvalidParam())
	bindingMonitorInstanceError = nil

	// 测试grafana client GetAllFolders返回错误
	getAllFoldersError = fmt.Errorf("get all folders failed")
	c.GetMeta()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInvalidParam())
	getAllFoldersError = nil
	errHandler.ErrorCode = nil

	// 测试grafana client Search返回错误
	searchError = fmt.Errorf("search failed")
	c.GetMeta()
	// 即使Search出错，也不会返回错误，而是记录日志并继续处理
	ast.Nil(errHandler.ErrorCode)
	searchError = nil

	// 验证结果数据是否正确设置
	c.GetMeta()
	ast.NotNil(c.Data["json"])
}
