{{- if .Values.CCEValidateWebhook.Enabled }}
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: cce-validate-webhook
  namespace: cce-system
  labels:
    app: cce-validate-webhook
spec:
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: cce-validate-webhook
  template:
    metadata:
      labels:
        app: cce-validate-webhook
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      serviceAccount: cce-validate-webhook-sa
      imagePullSecrets:
        - name: ccr-registry-secret
        - name: ccr-registry-secret-new
      restartPolicy: Always
      containers:
        - name: cce-validate-webhook
          image: {{.Values.CCEValidateWebhook.Image.ImageID}}
          imagePullPolicy: Always
          resources:
            limits:
              cpu: "4"
              memory: 16Gi
            requests:
              cpu: 200m
              memory: 200Mi
          args:
            - -beego-config=/home/<USER>/cce/cce-validate-webhook/conf/app.conf
            - -log-file=/home/<USER>/cce/cce-validate-webhook/logs/cce-validate-webhook.log
          volumeMounts:
            - name: cce-validate-webhook-config
              mountPath: /home/<USER>/cce/cce-validate-webhook/conf/
      volumes:
        - name: cce-validate-webhook-config
          configMap:
            defaultMode: 420
            name: cce-validate-webhook-config
{{- end }}