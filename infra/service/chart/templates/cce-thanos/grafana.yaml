{{- if .Values.CCEThanosEnabled }}
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: cce-monitor
  annotations:
    prometheus.io/scrape: 'true'
  labels:
    name: grafana
spec:
  type: NodePort
  selector:
    app.kubernetes.io/name: grafana
  ports:
    - port: 3000
      protocol: TCP
      targetPort: 3000
      nodePort: 8790
  sessionAffinity: None
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app.kubernetes.io/name: grafana
  name: grafana
  namespace: cce-monitor
spec:
  # must set as Parallel
  podManagementPolicy: Parallel
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/name: grafana
  serviceName: grafana
  template:
    metadata:
      labels:
        app.kubernetes.io/name: grafana
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      imagePullSecrets:
        - name: ccr-registry-secret
        - name: ccr-registry-secret-new
      initContainers:
        - name: cce-grafana-provisioning
          image: {{.Values.CCEThanos.Image.GrafanaProvisioningImageID}}
          command: ["/bin/sh", "-c", "cp -r /grafana/provisioning/* /cce/grafana/provisioning/"]
          imagePullPolicy: Always
          volumeMounts:
            - mountPath: /cce/grafana/provisioning
              name: cce-grafana-provisoning
      containers:
        - image: registry.baidubce.com/cce-plugin-pro/grafana:v7.5.0
          imagePullPolicy: Always
          lifecycle:
            postStart:
              exec:
                command: ["/bin/sh", "-c", "cp -r /cce/grafana/provisioning/* /etc/grafana/provisioning/"] 
          name: grafana
          args:
            - --homepath=/usr/share/grafana
            - --config=/etc/grafana/grafana.ini
          env:
            - name: GF_SECURITY_ADMIN_PASSWORD
              value: "cce@admin"
          ports:
            - containerPort: 3000
              name: http-grafana
              protocol: TCP
          securityContext:
            runAsUser: 0
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /robots.txt
              port: 3000
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 2
          resources:
            limits:
              memory: 4Gi
            requests:
              cpu: 100m
              memory: 2Gi
          volumeMounts:
            - mountPath: /var/lib/grafana
              name: grafana-data
            - mountPath: /cce/grafana/provisioning
              name: cce-grafana-provisoning
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      volumes:
        - name: grafana-data
          emptyDir: {}
        - name: cce-grafana-provisoning
          emptyDir: {}
  volumeClaimTemplates:
  - metadata:
      name: grafana-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "local-storage-grafana"
      resources:
        requests:
          storage: 100Gi
  updateStrategy:
    rollingUpdate:
      partition: 0
    type: RollingUpdate
{{- end }}