{{- if .Values.CCEConfigETCCrontabEnabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: cce-devops
  name: cce-config-manager-etc-crontab
data:
  crontab: |
    #!/bin/sh
    SHELL=/bin/bash
    PATH=/sbin:/bin:/usr/sbin:/usr/bin
    MAILTO=root
    HOME=/

    # For details see man 4 crontabs

    # Example of job definition:
    # .---------------- minute (0 - 59)
    # |  .------------- hour (0 - 23)
    # |  |  .---------- day of month (1 - 31)
    # |  |  |  .------- month (1 - 12) OR jan,feb,mar,apr ...
    # |  |  |  |  .---- day of week (0 - 6) (Sunday=0 or 7) OR sun,mon,tue,wed,thu,fri,sat
    # |  |  |  |  |
    # *  *  *  *  * user-name command to be executed

    # 清理 /home/<USER>/ 日志
    0 23 * * * root cd /home/<USER>/cce/ && find . -type f -name '*.log' -mtime +7 |grep -v docker | xargs rm -f

    # 清理 /home/<USER>/fluentd/logs/stdout.log 日志
    0 10 * * * root cd /home/<USER>/fluentd/logs/ && echo > stdout.log

    # 清理 cce-service 日志
    0 22 * * * root cd /home/<USER>/bce-api-service/bce-api-modules/api-service-cce/log/access_debug/ && find . -type d -name "bce-service-cce.access_debug.log*" -mtime +3 | xargs rm -rf
    0 21 * * * root cd /home/<USER>/bce-api-service/bce-api-modules/api-service-cce/log/debug && find . -type d -name "bce-service-cce.debug.log*" -mtime +3 | xargs rm -rf



---
apiVersion: extensions/v1beta1
kind: DaemonSet
metadata:
  namespace: cce-devops
  name: cce-config-manager-etc-crontab
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
  labels:
    app: cce
    control-plane: cce-config-manager-etc-crontab
spec:
  selector:
    matchLabels:
      app: cce
      control-plane: cce-config-manager-etc-crontab
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: cce
        control-plane: cce-config-manager-etc-crontab
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      imagePullSecrets:
        - name: ccr-registry-secret
        - name: ccr-registry-secret-new
      containers:
        - name: cce-config-manager-etc-crontab
          image: {{.Values.CCEConfigManager.Image.ImageID}}
          imagePullPolicy: Always
          args:
            - --filePath=/etc/crontab
            - --contentPath=/tmp/cce/crontab
            - --perm=0644
          resources:
            limits:
              cpu: "1"
              memory: 200Mi
            requests:
              cpu: 100m
              memory: 200Mi
          volumeMounts:
            - mountPath: /etc/crontab
              name: etc-crontab
            - mountPath: /tmp/cce
              name: cce-config-manager-etc-crontab
      hostNetwork: true
      restartPolicy: Always
      volumes:
        - hostPath:
            path: /etc/crontab
            type: ""
          name: etc-crontab
        - configMap:
            defaultMode: 420
            name: cce-config-manager-etc-crontab
          name: cce-config-manager-etc-crontab
  {{- end }}
