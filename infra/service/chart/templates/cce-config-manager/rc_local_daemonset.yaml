{{- if .Values.CCEConfigRCLocalEnabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: cce-devops
  name: cce-config-manager-rc-local
data:
  script: |
    #!/bin/sh
    touch /var/lock/subsys/local
    service rsyslog restart
    ethtool -G xgbe0 rx 4096 tx 4096
    bash -x /opt/nvme_boot/smp_affinity.sh
    bash -x /opt/eth_boot/smp_affinity_eth.sh
    /sbin/noah && /etc/init.d/noah start

    # K8S Master
    cd /home/<USER>/cce/cce-etcd && sh control start
    cd /home/<USER>/cce/apiserver && sh control start
    cd /home/<USER>/cce/controller-manager && sh control start
    cd /home/<USER>/cce/scheduler && sh control start

    # K8S Node
    initctl restart docker
    cd /home/<USER>/cce/kubelet && sh control start
    cd /home/<USER>/cce/kube-proxy && sh control start

    # CCE
    cd /home/<USER>/etcd/ && sudo -u work sh start.sh
    cd /home/<USER>/fluentd && sudo -u work sh control start
    cd /home/<USER>/bce-api-auth/bin && sudo -u work sh noah_control start
    cd /home/<USER>/alert_webhook/bin && sh noah_control start
    cd /home/<USER>/cce-chartmuseum/bin && sudo -u work sh noah_control start
    cd /home/<USER>/caas-api-manager/bin && sudo -u work sh noah_control start
    cd /home/<USER>/bce-api-service/bce-api-modules/api-service-cce/bin && sudo -u work sh start.sh
    
    # BBE
    cd /home/<USER>/bbe-manager/bin && sudo -u work sh noah_control start
    cd /home/<USER>/bbe-billling/bin && sudo -u work sh noah_control start
    cd /home/<USER>/caas-blockchain-manager/bin && sudo -u work sh noah_control start

    # BCC autoscaling
    cd /home/<USER>/bce-api-service/bce-api-modules/bce-auto-scaling/bin && sudo -u work sh start.sh

    # TTM
    cd /lib/modules/$(uname -r)/kernel/net/ttm &&  insmod newttm.ko
---
apiVersion: extensions/v1beta1
kind: DaemonSet
metadata:
  namespace: cce-devops
  name: cce-config-manager-rc-local
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
  labels:
    app: cce
    control-plane: cce-config-manager-rc-local
spec:
  selector:
    matchLabels:
      app: cce
      control-plane: cce-config-manager-rc-local
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: cce
        control-plane: cce-config-manager-rc-local
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      imagePullSecrets:
        - name: ccr-registry-secret
        - name: ccr-registry-secret-new
      containers:
        - name: cce-config-manager-rc-local
          image: {{.Values.CCEConfigManager.Image.ImageID}}
          imagePullPolicy: Always
          args:
            - --filePath=/etc/rc.d/rc.local
            - --contentPath=/tmp/cce/script
            - --perm=0644
          resources:
            limits:
              cpu: "1"
              memory: 200Mi
            requests:
              cpu: 100m
              memory: 200Mi
          volumeMounts:
            - mountPath: /etc/rc.d/rc.local
              name: rc-local
            - mountPath: /tmp/cce
              name: cce-config-manager-rc-local
      hostNetwork: true
      restartPolicy: Always
      volumes:
        - hostPath:
            path: /etc/rc.d/rc.local
            type: ""
          name: rc-local
        - configMap:
            defaultMode: 420
            name: cce-config-manager-rc-local
          name: cce-config-manager-rc-local
  {{- end }}
