{{- if .Values.CCEArtifactServiceEnabled }}
apiVersion: apps/v1
kind: DaemonSet
metadata:
  labels:
    app: cce-artifact-service
  name: cce-artifact-service
  namespace: cce-system
spec:
  selector:
    matchLabels:
      app: cce-artifact-service
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: cce-artifact-service
        control-plane: cce-artifact-service
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "{{.Values.CCEArtifactService.Config.HttpPort}}"
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      hostNetwork: true
      imagePullSecrets:
        - name: ccr-registry-secret
        - name: ccr-registry-secret-new
      containers:
        - image: {{.Values.CCEArtifactService.Image.ImageID}}
          imagePullPolicy: IfNotPresent
          name: cce-artifact-service
          args:
            - "--configFilePath=/home/<USER>/cce-artifact-service/conf/app.yaml"
            - "--validateRulePath=/home/<USER>/cce-artifact-service/conf/validate_rule.yaml"
          env:
            - name: TZ
              value: "Asia/Shanghai"
          volumeMounts:
            - name: config-volume
              mountPath: /home/<USER>/cce-artifact-service/conf
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - name: config-volume
          configMap:
            name: cce-artifact-service-config
---
apiVersion: v1
kind: Service
metadata:
  name: cce-artifact-service
  namespace: cce-system
spec:
  ports:
    - name: http
      port: {{.Values.CCEArtifactService.Config.HttpPort}}
      targetPort: {{.Values.CCEArtifactService.Config.HttpPort}}
  selector:
    app: cce-artifact-service
{{- end }}
