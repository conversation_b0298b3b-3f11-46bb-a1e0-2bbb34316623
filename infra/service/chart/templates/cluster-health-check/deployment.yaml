{{- if .Values.CCEClusterHealthCheckEnabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cce-cluster-health-check
  namespace: health-check
  labels:
    app: cce
    control-plane: cce-cluster-health-check
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
spec:
  replicas: 1
  strategy:  
    type: Recreate
  selector:
    matchLabels:
      app: cce
      control-plane: cce-cluster-health-check
  template:
    metadata:
      labels:
        app: cce
        control-plane: cce-cluster-health-check
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9500"
    spec:
        {{- if .Values.NodeSelector }}
        nodeSelector:
          {{.Values.Env}}: deploy
        {{- end }}
        imagePullSecrets:
        - name: ccr-registry-secret
        - name: ccr-registry-secret-new
        containers:
          - name: cce-cluster-health-check
            image: {{.Values.CCEClusterHealthCheck.Image.ImageID}}
            imagePullPolicy: Always
            args:
              - -cceServiceDB={{.Values.MySQLEndpoint.CCEServiceWrite}}
              - -periodSeconds=600
            env:
              - name: REGION
                value: "{{.Values.Env}}"
        hostNetwork: true
        serviceAccount: health-check
        restartPolicy: Always
{{- end }}
