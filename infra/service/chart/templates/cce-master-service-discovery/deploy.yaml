{{- if .Values.CCEMasterServiceDiscoveryEnabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-master-service-discovery-config
  namespace: cce-monitor
data:
  config.yaml: |
    {{.Values.Env}}:
{{- if .Values.CCEMasterServiceDiscoveryConfig }}
      minClusterSize: {{.Values.CCEMasterServiceDiscoveryConfig.minClusterSize | default 50 }}
{{- with .Values.CCEMasterServiceDiscoveryConfig.VIPAccountIDs }}
      VIPAccountIDs:
{{- toYaml . | nindent 8 }}
{{- end }}
      CCEServiceDB: {{.Values.MySQLEndpoint.CCEServiceWrite}}
{{- with .Values.CCEMasterServiceDiscoveryConfig.targetGroups }}
      targetGroups:
{{- toYaml . | nindent 8 }}
{{- end }}
{{- else }}
      miniClusterSize: 50
{{- end}}

---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
  labels:
    app: cce-master-service-discovery
  name: cce-master-service-discovery
  namespace: cce-monitor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cce-master-service-discovery
  template:
    metadata:
      labels:
        app: cce-master-service-discovery
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      serviceAccount: service-discovery
      hostNetwork: true
      imagePullSecrets:
        - name: ccr-registry-secret
        - name: ccr-registry-secret-new
      containers:
        - name: cce-monitor-service-discovery
          args:
            - -config=/config/config.yaml
            - -zone={{.Values.Env}}
          image: {{.Values.CCEMasterServiceDiscovery.Image.ImageID}}
          imagePullPolicy: Always
          volumeMounts:
            - name: cce-master-service-discovery-config
              mountPath: /config/
              readOnly: true
          resources:
            requests:
              cpu: "1"
              memory: "1Gi"
            limits:
              cpu: "4"
              memory: "4Gi"
      volumes:
        - configMap:
            defaultMode: 420
            name: cce-master-service-discovery-config
          name: cce-master-service-discovery-config
---
{{- end }}
