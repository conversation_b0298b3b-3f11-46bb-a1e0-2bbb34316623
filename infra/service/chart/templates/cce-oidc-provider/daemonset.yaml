{{- if .Values.CCEOIDCProviderEnabled }}
apiVersion: extensions/v1beta1
kind: DaemonSet
metadata:
  namespace: cce-system
  name: cce-oidc-provider
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
  labels:
    app: cce
    control-plane: cce-oidc-provider
spec:
  updateStrategy:  
    type: RollingUpdate
  selector:
    matchLabels:
      app: cce
      control-plane: cce-oidc-provider
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8848"
      labels:
        app: cce
        control-plane: cce-oidc-provider
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      {{- if not .Values.CCEImagePluginEnabled }}
      imagePullSecrets:	
      - name: ccr-registry-secret
      - name: ccr-registry-secret-new
      {{- end }}
      containers:
      - args:
        - -app-config=/home/<USER>/cce/cce-oidc-provider/conf/app.json
        - -beego-config=/home/<USER>/cce/cce-oidc-provider/conf/app.conf
        - -log-file=/home/<USER>/cce/cce-oidc-provider/logs/cce-oidc-provider.log
        name: cce-oidc-provider
        {{- if .Values.CCEOIDCProvider.Image.ImageDegist}}
        image: {{.Values.CCEOIDCProvider.Image.ImageDegist}}
        {{- else if .Values.CCEOIDCProvider.Image.ImageID }}
        image: {{.Values.CCEOIDCProvider.Image.ImageID}}
        {{- end }}
        imagePullPolicy: Always
        ports:
          - name: https
            containerPort: {{.Values.CCEOIDCProvider.Port}}
            hostPort: {{.Values.CCEOIDCProvider.Port}}
            protocol: TCP

        resources:
          limits:
            cpu: "1"
            memory: 4000Mi
          requests:
            cpu: 100m
            memory: 200Mi
        livenessProbe: # 健康检查：存活探针
          tcpSocket:
            port: {{.Values.CCEOIDCProvider.Port}}
          initialDelaySeconds: 20
          timeoutSeconds: 5
          periodSeconds: 5
        readinessProbe: # 健康检查：就绪探针
          tcpSocket:
            port: {{.Values.CCEOIDCProvider.Port}}
          initialDelaySeconds: 5
          timeoutSeconds: 5
          periodSeconds: 5
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
          - mountPath: /home/<USER>/cce/cce-oidc-provider/conf/
            name: cce-oidc-provider-config
          - mountPath: /home/<USER>/cce/cce-oidc-provider/logs/
            name: cce-oidc-provider-logs

      dnsPolicy: {{.Values.HostNetworkDNSPolicy}}
      hostNetwork: true
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 10
      volumes:
        - configMap:
            defaultMode: 420
            name: cce-oidc-provider-config
          name: cce-oidc-provider-config
        - hostPath:
            path: /home/<USER>/cce/cce-oidc-provider/logs/
            type: ""
          name: cce-oidc-provider-logs
{{- end }}