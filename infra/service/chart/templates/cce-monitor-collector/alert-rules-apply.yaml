{{- if .Values.CCEAlertRulesApply.Enable }}
---
apiVersion: v1
data:
  config: |
    apiVersion: v1
    clusters:
    - cluster:
        certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURuakNDQW9hZ0F3SUJBZ0lVZU91N2pJMUpzSUJ3cm9vUzdaZWVGcHVZcjNzd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1p6RUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFVcHBibWN4RURBT0JnTlZCQWNUQjBKbAphVXBwYm1jeERqQU1CZ05WQkFvVEJXcHdZV0Z6TVJRd0VnWURWUVFMRXd0amJHOTFaRzVoZEdsMlpURU9NQXdHCkExVUVBeE1GYW5CaFlYTXdIaGNOTWpFd09URXpNRFl5TnpBd1doY05NekV3T1RFeE1EWXlOekF3V2pCbk1Rc3cKQ1FZRFZRUUdFd0pEVGpFUU1BNEdBMVVFQ0JNSFFtVnBTbWx1WnpFUU1BNEdBMVVFQnhNSFFtVnBTbWx1WnpFTwpNQXdHQTFVRUNoTUZhbkJoWVhNeEZEQVNCZ05WQkFzVEMyTnNiM1ZrYm1GMGFYWmxNUTR3REFZRFZRUURFd1ZxCmNHRmhjekNDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFLdmJzQnBTY0ZmQUczSjUKOGtvdzZJMDVRbFdKNUxvd01Ea2lYTzJERWU0cVB6akExTHI0YnpDd2lpU1M0SUpwZDE1NXdsQjNMeE5RN0hIeQpVMm9oZHJkWmFDQ3dNRFVNelpUckt1Y1dKMkNwdEhZc2ZUM2JxUHdYS1dTclI0LzRsS1VRRDIweEdGVkZvNkVTClhubWczL0JiVmFuMEl6c2hnNVNrcDJlV0RYdlQyNTRKREVhN0NuazAxNWhIQjNCVmF4RlpqeDZXOUdrR0t3VUEKVUNLNnptZ3ZBS1gwUHVhT1oxSjFxTUdiakJibWZHd0VxeFdXbk1FK2dtL0UzUjdYM3NoZFBGa2h1dS9ad2RqUgpHcGw2UE1FMGczK29RaDkwS3J3ODhKWU9FeDdMRXdlNlNBQlk5NFY3Q2tNQXpkUTVLNWhDK1k4bnB4RUl1NXVPCkVndUlBRU1DQXdFQUFhTkNNRUF3RGdZRFZSMFBBUUgvQkFRREFnRUdNQThHQTFVZEV3RUIvd1FGTUFNQkFmOHcKSFFZRFZSME9CQllFRkxMN1RZZlNsQThENk56RU9DeDA0dWxOTVNjUU1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQgpBUUFzeTFsbWNzVWpQbi8wOTdLM0NBdE44V05RZlM2YlBjRUJTR0dxY1V5NGVFZ1ZQMk9ueTQwWFhkVEhDcXRTCjlrd0tYVHFEbWJEdUR6WGFHVS9rT3ZXOUJhNnZwUlFxWXRSN1BWdkJWYjBxN09DbTZGMFlGcjF3d1dZdGFwUS8KVm5mOG1oVmR1aEJxOWtWdGp3THpvK2NiM0c0bmthY1doNUEwUlF3Um9hMTNPZFk0bFVIRmZqc2pzY3ROeXd4MgphaHkvcVhwV3gvMnBHOGVpRmkxNEo0UDFtNUFHbTd1ZnJjcXFmeWdGb1cvQmx1azg3c1d5Smg1ZERvMnRDZTh0CmNuTnlwUjVqSlp2d2c3OEFMWjlIc1pGdG1nVW5rUlg1ZGVmM2JJTHJZY255WjFkb3gxMWhJYWd0UHJhbGNITjkKc0o0RXg4dVVENXU3NU83TDB4TlZVd1R2Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
        server: https://106.12.123.172:6443
      name: 02fa0a988caf43a9a5c0270310347581@kubernetes
    contexts:
    - context:
        cluster: 02fa0a988caf43a9a5c0270310347581@kubernetes
        user: cce-devops
      name: 02fa0a988caf43a9a5c0270310347581@kubernetes
    current-context: 02fa0a988caf43a9a5c0270310347581@kubernetes
    kind: Config
    preferences: {}
    users:
    - name: cce-devops
      user:
        client-certificate-data: 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
        client-key-data: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: ConfigMap
metadata:
  creationTimestamp: null
  name: apply-kubeconfig
  namespace: cce-monitor
---
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: alert-rules-apply
  namespace: cce-monitor
spec:
  schedule: '*/5 * * * *'
  concurrencyPolicy: Forbid
  failedJobsHistoryLimit: 1
  successfulJobsHistoryLimit: 1
  jobTemplate:
    metadata:
      labels:
        app: alert-rules-apply
    spec:
      template:
        metadata:
          labels:
            app: alert-rules-apply
        spec:
          volumes:
            - name: config
              configMap:
                name: apply-kubeconfig
                defaultMode: 420
          {{- if not .Values.CCEImagePluginEnabled }}
          imagePullSecrets:
            - name: ccr-registry-secret
            - name: ccr-registry-secret-new
          {{- end }}
          containers:
            - image: {{.Values.CCEAlertRulesApply.Image}}
              imagePullPolicy: Always
              name: apply
              command: [ 'kubectl' ]
              volumeMounts:
                - mountPath: /etc/kubeconfig/
                  name: config
              args:
                - apply
                - -f=/opt/alert-rules/
                - --kubeconfig=/etc/kubeconfig/config
          restartPolicy: Never
{{- end}}
