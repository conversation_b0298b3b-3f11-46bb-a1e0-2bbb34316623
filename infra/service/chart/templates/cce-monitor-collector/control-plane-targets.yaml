{{- if .Values.CCEControlPlaneTargets.Enable }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: control-plane-targets
  namespace: cce-monitor
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: control-plane-targets
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: control-plane-targets
    spec:
      {{- if not .Values.CCEImagePluginEnabled }}
      imagePullSecrets:
        - name: ccr-registry-secret
        - name: ccr-registry-secret-new
      {{- end }}
      containers:
        - args:
            - --namespace=cce-monitor
            - --etcd-port={{.Values.CCEControlPlaneTargets.ETCDPort}}
            - --enable-telegraf=false
          image: {{.Values.CCEControlPlaneTargets.Image}}
          imagePullPolicy: Always
          name: control-plane-targets
          resources:
            limits:
              cpu: "8"
              memory: 8Gi
            requests:
              cpu: 50m
              memory: 50Mi
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: control-plane-targets
      serviceAccountName: control-plane-targets
      terminationGracePeriodSeconds: 30
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: control-plane-targets
rules:
  - apiGroups:
      - '*'
    resources:
      - services
      - endpoints
      - namespaces
      - configmaps
    verbs:
      - list
      - watch
      - get
      - create
      - update
  - apiGroups:
      - certificates.k8s.io
    resources:
      - '*'
    verbs:
      - '*'
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: control-plane-targets
  namespace: cce-monitor
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: control-plane-targets
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: control-plane-targets
subjects:
  - kind: ServiceAccount
    name: control-plane-targets
    namespace: cce-monitor
{{- end }}