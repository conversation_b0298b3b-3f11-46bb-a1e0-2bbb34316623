{{- if .Values.CCEWorkflowControllerEnabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cce-workflow-controller
  namespace: cce-system
  labels:
    app: cce
    control-plane: cce-workflow-controller
spec:
  replicas: 1
  strategy:  
    type: Recreate
  selector:
    matchLabels:
      app: cce
      control-plane: cce-workflow-controller
  template:
    metadata:
      labels:
        app: cce
        control-plane: cce-workflow-controller
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9448"
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      imagePullSecrets:	
      - name: ccr-registry-secret
      - name: ccr-registry-secret-new
      initContainers:
        - name: cce-plugin-helm-chart
          image: {{.Values.CCEPluginHelmChart.Image.ImageID}}
          command: ["/bin/sh", "-c", "cp -r /plugin/charts/* /cce/plugin/charts/"]
          imagePullPolicy: Always
          volumeMounts:
            - mountPath: /cce/plugin/charts/
              name: cce-plugin-helm-chart
      containers:
        - name: cce-workflow-controller
          args:
            - -config=/home/<USER>/cce/cce-workflow-controller/conf/config.json
            - -log-file=/home/<USER>/cce/cce-workflow-controller/logs/cce-workflow-controller.log
            - -plugin-config=/home/<USER>/cce/cce-workflow-controller/plugin-conf/plugin-config.json
            - -flavor-config=/home/<USER>/cce/cce-cluster-controller/flavor-conf/config.json
          image: {{.Values.CCEWorkflowController.Image.ImageID}}
          imagePullPolicy: Always
          resources:
            limits:
              cpu: "4"
              memory: 8000Mi
            requests:
              cpu: 100m
              memory: 20Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /home/<USER>/cce/cce-workflow-controller/plugin-conf/
              name: cce-plugin-config
            - mountPath: /home/<USER>/cce/cce-workflow-controller/conf/
              name: cce-workflow-controller-config
            - mountPath: /home/<USER>/cce/cce-workflow-controller/logs/
              name: cce-workflow-controller-logs
            - mountPath: /cce/plugin/charts/
              name: cce-plugin-helm-chart
            - mountPath: /home/<USER>/cce/cce-workflow-controller/resource-manager/certs/
              name: cce-resource-manager-kafka-ca
            - mountPath: /home/<USER>/cce/cce-cluster-controller/flavor-conf/
              name: cce-cluster-flavor-config
      hostNetwork: true
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: cce-workflow-controller-serviceaccount
      terminationGracePeriodSeconds: 10
      volumes:
        - configMap:
            defaultMode: 420
            name: cce-plugin-config
          name: cce-plugin-config
        - configMap:
            defaultMode: 420
            name: cce-workflow-controller-config
          name: cce-workflow-controller-config
        - configMap:
            defaultMode: 420
            name: cce-cluster-flavor-config
          name: cce-cluster-flavor-config
        - hostPath:
            path: /home/<USER>/cce/cce-workflow-controller/logs/
            type: ""
          name: cce-workflow-controller-logs
        - emptyDir: {}
          name: cce-plugin-helm-chart
        - configMap:
            defaultMode: 420
            name: cce-resource-manager-kafka-ca
          name: cce-resource-manager-kafka-ca
{{- end }}
