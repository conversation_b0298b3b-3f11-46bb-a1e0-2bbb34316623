{{- if .Values.CCEStatusAlertEnabled }}
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  namespace: cce-devops
  name: cce-status-alert
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
spec:
  schedule: "*/5 * * * *"        # 每5分钟运行一次
  concurrencyPolicy: Allow       # 并发策略, 允许并行
  startingDeadlineSeconds: 300   # 300 秒内失败会重试重试
  failedJobsHistoryLimit: 5      # 保留失败的 Job 数量
  successfulJobsHistoryLimit: 5  # 保留成功的 Job 数量
  jobTemplate:
    spec:
      template:
        spec:
          {{- if .Values.NodeSelector }}
          nodeSelector:
            {{.Values.Env}}: deploy
          {{- end }}
          serviceAccount: cce-status-alert
          hostNetwork: true
          {{- if not .Values.CCEImagePluginEnabled }}
          imagePullSecrets:	
          - name: ccr-registry-secret
          - name: ccr-registry-secret-new
          {{- end }}
          containers:
            - name: cce-status-alert
              image: {{.Values.CCEStatusAlert.Image.ImageID}}
              imagePullPolicy: Always
              args:
                - -config=/config/conf.json
              volumeMounts:
                - name: cce-status-alert-config
                  mountPath: /config/
                  readOnly: true
                - name: cce-status-alert-logs
                  mountPath: /home/<USER>/cce/cce-status-alert/logs/
          volumes:
            - configMap:
                defaultMode: 420
                name: cce-status-alert-config
              name: cce-status-alert-config
            - hostPath:
                path: /home/<USER>/cce/cce-status-alert/logs/
                type: ""
              name: cce-status-alert-logs
          restartPolicy: OnFailure
  {{- end }}