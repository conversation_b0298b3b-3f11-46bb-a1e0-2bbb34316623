{{- if .Values.CCEVirtualKubelet.Enabled }}
apiVersion: extensions/v1beta1
kind: DaemonSet
metadata:
  namespace: cce-system
  name: cce-virtual-kubelet
  labels:
    app: cce
    control-plane: cce-virtual-kubelet
spec:
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: cce
      control-plane: cce-virtual-kubelet
  template:
    metadata:
      labels:
        app: cce
        control-plane: cce-virtual-kubelet
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      serviceAccount: cce-virtual-kubelet
      imagePullSecrets:
      - name: ccr-registry-secret
      - name: ccr-registry-secret-new
      containers:
      - name: bci-virtual-kubelet
        image: {{ .Values.CCEVirtualKubelet.Image }}
        imagePullPolicy: Always
        args: ["--enable-leader-election", "--provider", "baidu", "--nodename", "$(NODE_NAME)", "--log-level", "debug", "--metrics-addr", ":{{ .Values.CCEVirtualKubelet.MetricsPort }}"]
        env:
        - name: NODE_NAME
          value: "{{ .Values.CCEVirtualKubelet.NodeName }}"
        - name: LOG_ROTATE_COUNT
          value: "{{ .Values.CCEVirtualKubelet.LogMaxBackups }}"
        - name: KUBELET_PORT
          value: "{{ .Values.CCEVirtualKubelet.Port }}"
        - name: VKUBELET_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: VKUBELET_TAINT_KEY
          value: "virtual-kubelet.io/provider"
        - name: VKUBELET_TAINT_VALUE
          value: "baidu"
        - name: VKUBELET_TAINT_EFFECT
          value: "NoSchedule"
        - name: BCI_REGION
          value: "{{ .Values.Region }}"
        - name: BCI_LOGICAL_ZONE
          value: "zoneA" # bci实例所在可用区. e.g. zoneA/zoneB/zoneC
        - name: CCE_CLUSTER_ID
          value: "cce-{{ .Values.Env }}-meta" # cce集群id. e.g. c-6t1iySTF
        - name: BCI_VPC_ID
          value: "vpc-43zsdm46t9rp" # bci实例所在vpc短id. e.g. vpc-z1jv7cf7i4wv
        - name: BCI_VPC_UUID
          value: "6e8480cb-84ed-4c5a-ae4b-86e63c76dd03" # bci实例所在vpc长id. e.g. d27abb4c-6e73-4fc1-ae37-45e726c1de26
        - name: BCI_SUBNET_ID
          value: "sbn-vvqsb9b57f24" # bci实例所在子网短id. e.g. sbn-w8r6zbeeimpz，须在上面的vpc和可用区中
        - name: BCI_SUBNET_UUID
          value: "f035448c-5349-4b64-bfc4-041a5dcfc89b" # bci实例所在子网长id. e.g. 9b7c5311-e273-4e83-8796-1f5e57e0c226
        - name: BCI_SECURITY_GROUP_ID
          value: "g-1th78u4fyvez" # bci实例使用的安全组短id. e.g. g-8tam8t4udqec，须在上面的vpc中
        # cce鉴权服务配置
        - name: MULTI_TENANT_MODE
          value: "CCE"
        - name: CCE_AUTH_ENDPOINT
          value: "{{ .Values.BCESDKEndpoint.AuthEndpoint }}"
        - name: CHARGE_APPLICATION
          value: "{{ .Values.CCEVirtualKubelet.ChargeApplication }}"
        - name: CHARGE_ACCESS_KEY
          value: "{{ .Values.CCEVirtualKubelet.ChargeAccessKey }}"
        - name: CHARGE_SECRET_KEY
          value: "{{ .Values.CCEVirtualKubelet.ChargeSecretKey }}"
        - name: APISERVER_CERT_LOCATION
          value: /etc/virtual-kubelet/kubelet.pem
        - name: APISERVER_KEY_LOCATION
          value: /etc/virtual-kubelet/kubelet-key.pem
        - name: APISERVER_CA_CERT_LOCATION
          value: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        resources:
          limits:
            cpu: "1"
            memory: 4000Mi
          requests:
            cpu: 100m
            memory: 200Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - name: cce-virtual-kubelet-log
          mountPath: /log-volume
        - name: user-storage
          mountPath: /userstorage
        - name: ca-volume
          mountPath: /etc/virtual-kubelet/
      dnsPolicy: {{.Values.HostNetworkDNSPolicy}}
      hostNetwork: true
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 10
      volumes:
      - hostPath:
          path: /home/<USER>/cce/cce-virtual-kubelet/log
          type: DirectoryOrCreate
        name: cce-virtual-kubelet-log
      - hostPath:
          path: /home/<USER>/cce/cce-virtual-kubelet/user-storage
          type: DirectoryOrCreate
        name: user-storage
      - name: ca-volume
        hostPath:
          path: /home/<USER>/cce/ca/kubelet
          type: Directory
{{- end }}

