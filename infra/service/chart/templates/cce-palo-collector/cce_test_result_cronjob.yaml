{{- if .Values.CCEPaloCollectorTestResultEnabled }}
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  namespace: cce-devops
  name: cce-palo-collector-test-result
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
spec:
  schedule: "0 4 * * *"          # 每天凌晨 4 点运行一次
  concurrencyPolicy: Forbid      # 并发策略，第一个没执行完，跳过下一个
  failedJobsHistoryLimit: 2      # 保留失败的Job 数量
  successfulJobsHistoryLimit: 1  # 保留成功的Job 数量
  jobTemplate:
    spec:
      template:
        spec:
          imagePullSecrets:
            - name: ccr-registry-secret
            - name: ccr-registry-secret-new
          containers:
            - name: cce-palo-collector
              image: {{.Values.CCEPaloCollector.Image.ImageID}}
              imagePullPolicy: Always
              # env:
              #   - name: CollectTime
              #     value: "2020-10-21 15:15:00"
              args:
                - -region={{.Values.Env}}
                - -table-name=t_cce_test_result
                - -palo-table-name=t_cce_test_result
                - -palo-endpoint=admin:baidu123cce@tcp(100.66.2.117:9030)/cce_metadata
                - -palo-upload-url=100.66.3.91:8040
                - -mysql-endpoint=cce_devops:Ngvgzzo.CJds*Xg9rVH9@tcp(cce-devops-mysql.cce-devops:3306)/cce_devops?charset=utf8&parseTime=true
          restartPolicy: OnFailure
{{- end }}
