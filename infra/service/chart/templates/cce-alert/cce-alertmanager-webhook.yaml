{{- if .Values.CCEAlert.Enabled }}
kind: Service
apiVersion: v1
metadata:
  name: cce-alertmanager-webhook
  namespace: cce-monitor
  labels:
    app.kubernetes.io/name: cce-alertmanager-webhook
spec:
  type: ClusterIP
  selector:
    app.kubernetes.io/name: cce-alertmanager-webhook
  ports:
    - name: web
      protocol: TCP
      port: 9094
      targetPort: web
---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: cce-alertmanager-webhook
  namespace: cce-monitor
  labels:
    app.kubernetes.io/name: cce-alertmanager-webhook
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: cce-alertmanager-webhook
  template:
    metadata:
      labels:
        app.kubernetes.io/name: cce-alertmanager-webhook
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      imagePullSecrets:
        - name: ccr-registry-secret
        - name: ccr-registry-secret-new
      containers:
        - name: alertmanager-webhook
          image: {{.Values.CCEAlert.Image.AlertManagerWebhookImageID}}
          imagePullPolicy: Always
          args:
            - -beego-config=/home/<USER>/cce/cce-monitor/conf/app.conf
            - -log-file=/home/<USER>/cce/cce-monitor/logs/cce-alertmanager-webhook.log
          ports:
            - containerPort: 9094
              name: web
              protocol: TCP
          volumeMounts:
            - mountPath: /home/<USER>/cce/cce-monitor/conf
              name: cce-alertmanager-webhook-config
              readOnly: true
      volumes:
        - name: cce-alertmanager-webhook-config
          configMap:
            name: cce-alertmanager-webhook-config
{{- end }}