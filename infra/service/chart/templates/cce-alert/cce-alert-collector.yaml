{{- if .Values.CCEAlert.Enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cce-alert-collector
  namespace: cce-monitor
  labels:
    app.kubernetes.io/name: cce-alert-collector
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: cce-alert-collector
  template:
    metadata:
      labels:
        app.kubernetes.io/name: cce-alert-collector
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      serviceAccountName: cce-alert-collector
      imagePullSecrets:
        - name: ccr-registry-secret
        - name: ccr-registry-secret-new
      containers:
        - name: cce-alert-collector
          image: {{.Values.CCEAlert.Image.AlertCollectorImageID}}
          imagePullPolicy: Always
          args:
            - -config=/home/<USER>/cce/cce-monitor/conf/cce-alert-collector.conf
            - -log=/home/<USER>/cce/cce-monitor/logs/cce-alert-collector.log
          volumeMounts:
            - mountPath: /home/<USER>/cce/cce-monitor/conf
              name: cce-alert-collector-config
              readOnly: true
      volumes:
        - name: cce-alert-collector-config
          configMap:
            name: cce-alert-collector-config
{{- end }}