{{- if .Values.CCEAPPServiceEnabled }}
apiVersion: extensions/v1beta1
kind: DaemonSet
metadata:
  namespace: cce-system
  name: cce-app-service
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
  labels:
    app: cce
    control-plane: cce-app-service
spec:
  updateStrategy:  
    type: RollingUpdate
  selector:
    matchLabels:
      app: cce
      control-plane: cce-app-service
  template:
    metadata:
      labels:
        app: cce
        control-plane: cce-app-service
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "{{.Values.CCEAPPService.Port}}"
        prometheus.io/path: "/metrics"
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      {{- if not .Values.CCEImagePluginEnabled }}
      imagePullSecrets:	
      - name: ccr-registry-secret
      - name: ccr-registry-secret-new
      {{- end }}
      containers:
      - name: cce-app-service
        image: {{.Values.CCEAPPService.Image.ImageID}}
        imagePullPolicy: Always
        env:
          - name: INGRESS_CONTROLLER_IMAGE
            value: {{.Values.CCEAPPService.Config.CCEIngressControllerImageID}}
        args:
          - --insecure-bind-address=0.0.0.0
          - --insecure-port={{.Values.CCEAPPService.Port}}
          - --auth-endpoint={{.Values.BCESDKEndpoint.AuthEndpoint}}
          - --iam-endpoint={{.Values.BCESDKEndpoint.IAMEndpoint}}
          {{- if .Values.CCEIAMDisasterToleranceEnabled }}
          - --iam-agent-endpoint={{.Values.BCESDKEndpoint.IAMAgentEndpoint}}
          - --iam-disaster-tolerance-enabled={{.Values.CCEIAMDisasterToleranceEnabled}}
          {{- end }}
          - --sts-endpoint={{.Values.BCESDKEndpoint.STSEndpoint}}
          - --cce-oidc-provider-endpoint={{.Values.BCESDKEndpoint.CCEOIDCProviderEndpoint}}
          - --cce-oidc-issuer={{.Values.CCEOIDCProvider.Config.Issuer}}
          - --service-role-name={{.Values.CCEServiceAccount.ServiceRoleName}}
          - --service-name={{.Values.CCEServiceAccount.ServiceName}}
          - --service-password={{.Values.CCEServiceAccount.ServicePassword}}
          - --cce-service-db={{.Values.MySQLEndpoint.CCEServiceWrite}}
          - --log-file=/home/<USER>/cce/cce-app-service/logs/cce-app-service.log
          - --blb-endpoint={{.Values.BCESDKEndpoint.BLBOpenAPIEndpoint}}
          - --eip-endpoint={{.Values.BCESDKEndpoint.EIPOpenAPIEndpoint}}
          - --vpc-endpoint={{.Values.BCESDKEndpoint.VPCEndpoint}}
          - --open-cce-gateway-endpoint={{.Values.BCESDKEndpoint.OpenCCEGatewayEndpoint}}
          - --cce-v2-endpoint={{.Values.BCESDKEndpoint.CCEV2Endpoint}}
          - --tag-endpoint={{.Values.BCESDKEndpoint.TagEndpoint}}
          - --ingress-controller-image={{.Values.CCEAPPService.Config.CCEIngressControllerImageID}}
          - --eip-purchase-type={{.Values.EIPPurchaseType}}
          - --region={{.Values.Region}}
        {{- if .Values.EIPBillingMethod }}
          - --eip-billing-method={{ .Values.EIPBillingMethod }}
        {{- end}}
        ports:
        - containerPort: {{.Values.CCEAPPService.Port}}
          hostPort: {{.Values.CCEAPPService.Port}}
          name: cce-app-service
          protocol: TCP
        resources:
          limits:
            cpu: "4"
            memory: 16000Mi
          requests:
            cpu: 100m
            memory: 200Mi
        livenessProbe: # 健康检查：存活探针
          tcpSocket:
            port: {{.Values.CCEAPPService.Port}}
          initialDelaySeconds: 20
          timeoutSeconds: 5
          periodSeconds: 5
        readinessProbe: # 健康检查：就绪探针
          tcpSocket:
            port: {{.Values.CCEAPPService.Port}}
          initialDelaySeconds: 5
          timeoutSeconds: 5
          periodSeconds: 5
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /home/<USER>/cce/cce-app-service/logs/
          name: cce-app-service-logs
      dnsPolicy: {{.Values.HostNetworkDNSPolicy}}
      hostNetwork: true
      restartPolicy: Always
      serviceAccount: cce-app-service-serviceaccount
      volumes:
      - hostPath:
          path: /home/<USER>/cce/cce-app-service/logs/
          type: ""
        name: cce-app-service-logs
{{- end }}
