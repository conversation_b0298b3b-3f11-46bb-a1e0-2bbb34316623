{{- if .Values.CCEMasterLBControllerEnabled }}
apiVersion: extensions/v1beta1
kind: DaemonSet
metadata:
  name: cce-master-lb-controller-{{.Values.CCEMasterLBController.MetaClusterID}}
  namespace: cce-system
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
  labels:
    app: cce
    control-plane: cce-master-lb-controller-{{.Values.CCEMasterLBController.MetaClusterID}}
spec:
  updateStrategy:  
    type: RollingUpdate
  selector:
    matchLabels:
      app: cce
      control-plane: cce-master-lb-controller-{{.Values.CCEMasterLBController.MetaClusterID}}
  template:
    metadata:
      labels:
        app: cce
        control-plane: cce-master-lb-controller-{{.Values.CCEMasterLBController.MetaClusterID}}
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "10403"
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      {{- if not .Values.CCEImagePluginEnabled }}
      imagePullSecrets:	
      - name: ccr-registry-secret
      - name: ccr-registry-secret-new
      {{- end }}
      containers:
        - name: cce-master-lb-controller
          args:
            - --log-file=/home/<USER>/cce/cce-master-lb-controller/logs/cce-master-lb-controller.log
            - --config-file=/home/<USER>/cce/cce-master-lb-controller/conf/config.json
            - --region={{.Values.Env}}
            {{- if .Values.CCEMasterLBController.EnableMetricsPort }}
            - --metrics-port={{.Values.CCEMasterLBController.MetricsPort}}
            {{- end }}
            - --meta-cluster-id={{.Values.CCEMasterLBController.MetaClusterID}}
          image: {{.Values.CCEMasterLBController.Image.ImageID}}
          env:
            - name: SET_DEBUG
              value: "true"
            - name: EIP_ENDPOINT
              value: {{.Values.BCESDKEndpoint.EIPOpenAPIEndpoint}}
            - name: BLB_ENDPOINT
              value: {{.Values.BCESDKEndpoint.BLBOpenAPIEndpoint}}
            - name: STS_ENDPOINT
              value: {{.Values.BCESDKEndpoint.STSEndpoint}}
            - name: IAM_ENDPOINT
              value: {{.Values.BCESDKEndpoint.IAMEndpoint}}
            {{- if .Values.CCEIAMDisasterToleranceEnabled }}
            - name: IAM_DISASTER_TOLERANCE_ENABLED
              value: "true"
            - name: IAM_AGENT_ENDPOINT
              value: {{.Values.BCESDKEndpoint.IAMAgentEndpoint}}
            {{- end }}
          imagePullPolicy: Always
          resources:
            limits:
              cpu: "4"
              memory: 20000Mi
            requests:
              cpu: 100m
              memory: 20Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /home/<USER>/cce/cce-master-lb-controller/logs/
              name: cce-master-lb-controller-logs
            - mountPath: /home/<USER>/cce/cce-master-lb-controller/conf/
              name: cce-master-lb-controller-config
      dnsPolicy: {{.Values.HostNetworkDNSPolicy}}
      hostNetwork: true
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: cce-master-lb-controller-serviceaccount-{{.Values.CCEMasterLBController.MetaClusterID}}
      terminationGracePeriodSeconds: 10
      volumes:
        - hostPath:
            path: /home/<USER>/cce/cce-master-lb-controller/logs/
            type: ""
          name: cce-master-lb-controller-logs
        - configMap:
            defaultMode: 420
            name: cce-master-lb-controller-config-{{.Values.CCEMasterLBController.MetaClusterID}}
          name: cce-master-lb-controller-config
{{- end }}