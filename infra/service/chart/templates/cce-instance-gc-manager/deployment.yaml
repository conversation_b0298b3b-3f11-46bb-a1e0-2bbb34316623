{{- if .Values.CCEInstanceGCManager.Enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cce-instance-gc-manager
  namespace: cce-system
  labels:
    app.kubernetes.io/name: cce-instance-gc-manager
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: cce-instance-gc-manager
  template:
    metadata:
      labels:
        app.kubernetes.io/name: cce-instance-gc-manager
      annotations:
          prometheus.io/scrape: "true"
          prometheus.io/port: "9823"
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      serviceAccountName: cce-instance-gc-manager
      imagePullSecrets:
        - name: ccr-registry-secret
        - name: ccr-registry-secret-new
      containers:
        - name: cce-instance-gc-manager
          image: {{.Values.CCEInstanceGCManager.Image.CCEInstanceGCManagerImageID}}
          imagePullPolicy: Always
          env:
          - name: POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          resources:
            limits:
              cpu: "4"
              memory: 16Gi
            requests:
              cpu: 200m
              memory: 200Mi
          args:
            - -config=/home/<USER>/cce/instance-gc-manager/conf/cce-instance-gc-manager.conf
            - -log=/home/<USER>/cce/cce-instance-gc-manager/logs/cce-instance-gc-manager.log
          volumeMounts:
            - mountPath: /home/<USER>/cce/instance-gc-manager/conf
              name: cce-instance-gc-manager-config
              readOnly: true
            - mountPath: /home/<USER>/cce/cce-instance-gc-manager/logs
              name: cce-instance-gc-manager-logs
      volumes:
        - name: cce-instance-gc-manager-config
          configMap:
            name: cce-instance-gc-manager-config
        - hostPath:
            path: /home/<USER>/cce/cce-instance-gc-manager/logs/
            type: ""
          name: cce-instance-gc-manager-logs
{{- end }}