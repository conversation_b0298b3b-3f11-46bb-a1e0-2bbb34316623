{{- if .Values.CCEGrafana.Enabled }}
apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  labels:
    app: grafana
  name: cce-grafana
  namespace: cce-monitor
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: grafana
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: grafana
    spec:
      containers:
        - args:
            - --homepath=/usr/share/grafana
            - --config=/etc/grafana/grafana.ini
          env:
            - name: GF_USERS_VIEWERS_CAN_EDIT
              value: "true"
            - name: GF_SECURITY_ADMIN_PASSWORD
              value: ccegrafana@admin
            - name: GF_DATABASE_TYPE
              value: postgres
            - name: GF_DATABASE_HOST
              value: postgresql10.rdsgn55n5zxpo9q.rds.gz.baidubce.com:3306
            - name: GF_DATABASE_USER
              value: cprom
            - name: GF_DATABASE_PASSWORD
              value: avbd123456
            - name: GF_DATABASE_NAME
              value: postgres
          image: registry.baidubce.com/cce-plugin-pro/grafana:v7.5.17
          imagePullPolicy: Always
          lifecycle:
            postStart:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - cp -r /cce/grafana/provisioning/* /etc/grafana/provisioning/ && cp -r  /cce/grafana/provisioning/plugins /var/lib/grafana/
          name: grafana
          ports:
            - containerPort: 3000
              name: http-grafana
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /robots.txt
              port: 3000
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 2
          resources:
            limits:
              cpu: "1"
              memory: 2Gi
            requests:
              cpu: 100m
              memory: 128Mi
          securityContext:
            procMount: Default
            runAsUser: 0
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /cce/grafana/provisioning
              name: cce-grafana-provisoning
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: ccr-registry-secret
        - name: ccr-registry-secret-new
      initContainers:
        - command:
            - /bin/sh
            - -c
            - cp -r /grafana/provisioning/* /cce/grafana/provisioning/
          image: {{.Values.CCEGrafana.GrafanaProvisioningImage}}
          imagePullPolicy: Always
          name: cce-grafana-provisioning
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /cce/grafana/provisioning
              name: cce-grafana-provisoning
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - emptyDir: {}
          name: cce-grafana-provisoning
  {{- end }}
