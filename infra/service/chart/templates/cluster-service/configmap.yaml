{{- if .Values.CCEClusterServiceEnabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: cce-system
  name: cce-cluster-service-config
data:
  app.conf: |
    appname = cce-cluster-service
    httpport = {{.Values.CCEClusterService.Port}}
    autorender = false
    copyrequestbody = true
    EnableDocs = true
  app.json: |
    {
      "Region": "{{.Values.Env}}",
      "Handler": "{{.Values.CCEClusterService.Config.Handler}}",
      "EnableUpgradeWorkflow": {{.Values.CCEClusterService.Config.EnableUpgradeWorkflow}},
      "ClientConfig": {
         "Region": "{{.Values.Region}}",
         "BCESDKTimeout": 15,
         "ServiceName": "{{.Values.CCEServiceAccount.ServiceName}}",
         "ServiceRoleName": "{{.Values.CCEServiceAccount.ServiceRoleName}}",
         "ServicePassword": "{{.Values.CCEServiceAccount.ServicePassword}}",
         "ServiceIAMPermissionCheckName": "{{.Values.CCEServiceAccount.ServiceIAMPermissionCheckName}}",
         "IAMDisasterToleranceEnabled": {{.Values.CCEIAMDisasterToleranceEnabled}},
         "GianoToken": "{{.Values.GianoToken}}",
         "MySQLEndpoint": "{{.Values.MySQLEndpoint.CCEServiceWrite}}",
         "CenterMySQLEndpoint": "{{.Values.MySQLEndpoint.CCECenterDBWrite}}",
         "Endpoints":{
             "STSEndpoint": "{{.Values.BCESDKEndpoint.STSEndpoint}}",
             "IAMEndpoint": "{{.Values.BCESDKEndpoint.IAMEndpoint}}",
             "IAMAgentEndpoint": "{{.Values.BCESDKEndpoint.IAMAgentEndpoint}}",
             "PriceEndpoint": "{{.Values.BCESDKEndpoint.PriceEndpoint}}",
             "VPCEndpoint": "{{.Values.BCESDKEndpoint.VPCEndpoint}}",
             "VPCInternalEndpoint": "{{.Values.BCESDKEndpoint.VPCInternalEndpoint}}",
             "BLBLogicEndpoint": "{{.Values.BCESDKEndpoint.BLBLogicEndpoint}}",
             "AppBLBOpenAPIEndpoint": "{{.Values.BCESDKEndpoint.AppBLBOpenAPIEndpoint}}",
             "EIPEndpoint": "{{.Values.BCESDKEndpoint.EIPOpenAPIEndpoint}}",
             "ImageLogicEndpoint": "{{.Values.BCESDKEndpoint.ImageLogicEndpoint}}",
             "ImageEndpoint": "{{.Values.BCESDKEndpoint.ImageEndpoint}}",
             "BCCEndpoint": "{{.Values.BCESDKEndpoint.BCCEndpoint}}",
             "ResourceManagerEndpoint": "{{.Values.BCESDKEndpoint.ResourceManagerEndpoint}}",
             "BBCEndpoint": "{{.Values.BCESDKEndpoint.BBCEndpoint}}",
             "BCCLogicEndpoint": "{{.Values.BCESDKEndpoint.BCCLogicEndpoint}}",
             "BCCNeutronEndpoint": "{{.Values.BCESDKEndpoint.BCCNeutronEndpoint}}",
             "HPASInternalEndpoint": "{{.Values.BCESDKEndpoint.HPASInternalEndpoint}}",
             "HPASEndpoint": "{{.Values.BCESDKEndpoint.HPASEndpoint}}",
             "UserSettingEndpoint": "{{.Values.BCESDKEndpoint.UserSettingEndpoint}}",
             "QuotaCenterEndpoint": "{{.Values.BCESDKEndpoint.QuotaCenterEndpoint}}",
             "AuthEndpoint": "{{.Values.BCESDKEndpoint.AuthEndpoint}}",
             "CCEOIDCProviderEndpoint": "{{.Values.BCESDKEndpoint.CCEOIDCProviderEndpoint}}",
             "CCEOIDCIssuer": "{{.Values.CCEOIDCProvider.Config.Issuer}}",
             "BECEndpoint": "{{ .Values.BCESDKEndpoint.BECEndpoint }}",
             "IAMForBECEndpoint": "{{ .Values.BCESDKEndpoint.IAMForBECEndpoint }}",
             "FinanceEndpoint": "{{ .Values.BCESDKEndpoint.FinanceEndpoint }}",
             "CPromEndpoint": "{{ .Values.BCESDKEndpoint.CPromEndpoint }}",
             "CPromV2Endpoint": "{{ .Values.BCESDKEndpoint.CPromV2Endpoint }}",
             "APPServiceEndpoint": "{{ .Values.BCESDKEndpoint.APPServiceEndpoint }}",
             "QualifyEndpoint": "{{ .Values.BCESDKEndpoint.QualifyEndpoint }}",
             "ZoneEndpoint": "{{ .Values.BCESDKEndpoint.ZoneEndpoint }}",
             "CRMEndpoint": "{{ .Values.BCESDKEndpoint.CRMEndpoint }}",
             "TagEndpoint": "{{ .Values.BCESDKEndpoint.TagEndpoint }}",
             "LogicTagEndpoint": "{{ .Values.BCESDKEndpoint.LogicTagEndpoint }}",
             "NeutronEndpoint": "{{ .Values.BCESDKEndpoint.NeutronEndpoint }}",
             "OpenCCEGatewayEndpoint": "{{ .Values.BCESDKEndpoint.OpenCCEGatewayEndpoint }}",
             "BOSEndpoint": "{{ .Values.BCESDKEndpoint.BOSEndpoint }}",
             "ECCREndpoint": "{{ .Values.BCESDKEndpoint.ECCREndpoint }}",
             "CCEV2EndpointForTag": "{{ .Values.BCESDKEndpoint.CCEV2EndpointForTag }}",
             "BusEndpoint": "{{ .Values.BCESDKEndpoint.BusEndpoint }}",
             "ArtifactServiceEndpoint": "http://cce-artifact-service.cce-system.svc:{{.Values.CCEArtifactService.Config.HttpPort}}",
             "KMSEndpoint": "{{ .Values.BCESDKEndpoint.KMSEndpoint }}",
             "GianoEndpoint": "{{ .Values.BCESDKEndpoint.GianoEndpoint }}",
             "ResourceManagerEndpoint": "{{ .Values.BCESDKEndpoint.ResourceManagerEndpoint }}",
             "ResourceManagerKafkaConfig":{
                    "CaPath": "/home/<USER>/cce/cce-cluster-service/{{.Values.CCEResourceGroupKafka.TLS.CaPath}}",
                    "CrtPath": "/home/<USER>/cce/cce-cluster-service/{{.Values.CCEResourceGroupKafka.TLS.CrtPath}}",
                    "KeyPath": "/home/<USER>/cce/cce-cluster-service/{{.Values.CCEResourceGroupKafka.TLS.KeyPath}}",
                    "Endpoint": "{{.Values.CCEResourceGroupKafka.Endpoint }}",
                    "Topic": "{{.Values.CCEResourceGroupKafka.Topic}}"
                }
         }
      },
      "DefaultMasterConfig":{
         "InstanceType": "{{.Values.CCEClusterService.Config.DefaultMasterConfig.InstanceType}}",
         "FlavorSmallCPU": {{.Values.CCEClusterService.Config.DefaultMasterConfig.FlavorSmallCPU}},
         "FlavorSmallMEM": {{.Values.CCEClusterService.Config.DefaultMasterConfig.FlavorSmallMEM}},
         "FlavorMediumCPU": {{.Values.CCEClusterService.Config.DefaultMasterConfig.FlavorMediumCPU}},
         "FlavorMediumMEM": {{.Values.CCEClusterService.Config.DefaultMasterConfig.FlavorMediumMEM}},
         "FlavorLargeCPU": {{.Values.CCEClusterService.Config.DefaultMasterConfig.FlavorLargeCPU}},
         "FlavorLargeMEM": {{.Values.CCEClusterService.Config.DefaultMasterConfig.FlavorLargeMEM}},
         "RootDiskType": "{{.Values.CCEClusterService.Config.DefaultMasterConfig.RootDiskType}}",
         "RootDiskSize": {{.Values.CCEClusterService.Config.DefaultMasterConfig.RootDiskSize}},
         "ImageType": "{{.Values.CCEClusterService.Config.DefaultMasterConfig.ImageType}}",
         "ImageName": "{{.Values.CCEClusterService.Config.DefaultMasterConfig.ImageName}}",
         "OSType": "{{.Values.CCEClusterService.Config.DefaultMasterConfig.OSType}}",
         "OSName": "{{.Values.CCEClusterService.Config.DefaultMasterConfig.OSName}}",
         "OSArch": "{{.Values.CCEClusterService.Config.DefaultMasterConfig.OSArch}}",
         "OSVersion": "{{.Values.CCEClusterService.Config.DefaultMasterConfig.OSVersion}}"
      },
      "SkipUpdateInstanceGroupModel": {{ .Values.CCEClusterService.Config.SkipUpdateInstanceGroupModel }},
      "EnableEdgeHubInCloudEdgeCluster": {{ .Values.CCEClusterService.Config.EnableEdgeHubInCloudEdgeCluster }},
      "ServerlessAvailable": {{ .Values.CCEClusterService.Config.ServerlessAvailable | default false }},
      "IsUnionPayEnv": {{.Values.IsUnionPayEnv}},
      "ManagedMetaClusterID": "{{.Values.ManagedMetaClusterID}}",
      "ManagedProEnable": {{.Values.ManagedProEnable}},
      "InstanceDeployType": "{{.Values.InstanceDeployType}}",
      "EnableTag": {{.Values.EnableTag}},
      "ValidateSubuserPermission": {{.Values.ValidateSubuserPermission | default true}},
      "RecommendNodeScale": 64,
      "RecommendPodScale": 64,
      "AddOnMetaCacheSeconds": 3600,
      {{- with .Values.CCEClusterService.Config }}
      "DefaultServerlessMasterConfig":{
          "Containers":{
              "apiserver":{
                  "name":"apiserver",
                  "image":{{ .DefaultServerlessMasterConfig.APIServerImage | default "registry.baidubce.com/serverless-k8s/kube-apiserver" | quote }},
                  "cpu":"1",
                  "memory":"2Gi"
              },
              "controller-manager":{
                  "name":"controller-manager",
                  "image":{{ .DefaultServerlessMasterConfig.ControllerManangerImage | default "registry.baidubce.com/serverless-k8s/kube-controller-manager" | quote }},
                  "cpu":"1",
                  "memory":"2Gi"
              },
              "scheduler":{
                  "name":"scheduler",
                  "image":{{ .DefaultServerlessMasterConfig.SchedulerImage | default "registry.baidubce.com/serverless-k8s/kube-scheduler" | quote }},
                  "cpu":"1",
                  "memory":"2Gi"
              },
              "virtual-kubelet":{
                  "name":"virtual-kubelet",
                  "image":{{ .DefaultServerlessMasterConfig.VirtualKubeletImage | default "registry.baidubce.com/serverless-k8s/virtual-kubelet" | quote }},
                  "cpu":"1",
                  "memory":"2Gi"
              },
              "debug":{
                  "name":"debug",
                  "image":{{ .DefaultServerlessMasterConfig.DebugImage | default "registry.baidubce.com/serverless-k8s/debug" | quote }},
                  "cpu":"0.25",
                  "memory":"0.5Gi"
              }
          }
      },
      "ServerlessMasterNoClusterIPAPIServer": {
          "name":"apiserver",
          "image":{{ .DefaultServerlessMasterConfig.APIServerNoClusterIPImage | default "registry.baidubce.com/serverless-k8s/kube-apiserver-no-clusterip" | quote }},
          "cpu":"1",
          "memory":"2Gi"
      },
      "ServerlessMasterServiceController": {
          "name": "service-controller",
          "image": {{ .DefaultServerlessMasterConfig.ServiceControllerImage | default "registry.baidubce.com/serverless-k8s/cce-service-controller" | quote }},
          "cpu":"500m",
          "memory": "1Gi"
      },
      "ServerlessMasterBECVirtualKubelet": {
          "name": "bec-virtual-kubelet",
          "image": {{ .DefaultServerlessMasterConfig.BECVirtualKubeletImage | default "registry.baidubce.com/cce-edge/virtual-kubelet:latest" | quote }},
          "cpu":"1",
          "memory": "2Gi"
      },
      "ServerlessMasterBECServiceController": {
          "name": "bec-service-controller",
          "image": {{ .DefaultServerlessMasterConfig.BECServiceControllerImage | default "registry.baidubce.com/cce-edge/cloud-provider-bec:latest" | quote }},
          "cpu":"500m",
          "memory": "1Gi"
      },
      {{- end }}
      "SupportedOSVersions": [
          "7.1",
          "7.2",
          "7.3",
          "7.4",
          "7.5",
          "7.6",
          "7.7",
          "7.8",
          "7.9",
          "8.0",
          "8.4",
          "8.4-U1",
          "16.04 LTS",
          "18.04 LTS",
          "22.04 LTS",
          "24.04 LTS",
          "20.04-U1 LTS",
          "22.04-U1 LTS",
          "22.04 LTS-BF3",
          "8.6",
          "9.2",
          "9.3",
          "9.3-U1"
      ],
      "SupportedOSVersionsForARM": [
          "7.6"
      ],
      "SupportedOSVersionsInWL": [
          "20.04 LTS"
      ],
      "UnSupportedInstanceTypes": [
          "8",
          "23",
          "24",
          "26"
      ],
      "SupportedImageTypes": [
          "common",
          "custom",
          "sharing",
          "gpuBccImage",
          "gpuBccCustom",
          "bbcCommon",
          "bbcCustom"
      ],
      "SupportedK8SVersions": [
          "1.6.2",
          "1.8.6",
          "1.8.12",
          "1.11.1",
          "1.11.5",
          "1.13.4",
          "1.13.10",
          "1.14.9",
          "1.16.3",
          "1.16.8",
          "1.17.17",
          "1.18.9",
          "1.18.9-bilibili-mixprotocols",
          "1.20.8",
          "1.20.8-arm64",
          "1.21.14",
          "1.22.5",
          "1.24.4",
          "1.26.9"
      ],
      "AvailableK8SVersions": [
          "1.20.8",
          "1.21.14",
          "1.22.5",
          "1.24.4",
          "1.26.9",
          "1.28.8",
          "1.30.1",
          "1.31.1"
      ],
      "K8SVersionsForServerless": [
          "1.16.8"
      ],
      "K8SVersionsForCloudEdge": [
          "1.18.9"
      ],
      "K8SVersionsForARM": [
          "1.20.8"
      ],
      "DefaultOSVersions": {
          "common": [
              "CentOS:7.5",
              "Ubuntu:16.04 LTS"
          ],
          "gpuBccImage": [
              "CentOS:7.5",
              "Ubuntu:16.04 LTS"
          ],
          "bbcCommon": [
              "CentOS:7.5",
              "Ubuntu:16.04 LTS"
          ]
      },
      "EBPFSupportedImage": {
          "BaiduLinux": [
              "3.0"
          ],
          "Ubuntu": [
              "20.04 LTS",
              "22.04 LTS"
          ]
      },
      "ClusterBLBEIPBandwidthInMbps": 1,
      "CCEManagedMasterSGRules": [
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "************/24",
              "DestIP": "",
              "PortRangeMin": 2379,
              "PortRangeMax": 2380,
              "Remark": "etcd"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 6443,
              "PortRangeMax": 6443,
              "Remark": "apiserver"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "{{.Values.CCEClusterService.Config.RegionNetworkSegment}}",
              "DestIP": "",
              "PortRangeMin": 22,
              "PortRangeMax": 22,
              "Remark": "networkSegment"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "**********/21",
              "DestIP": "",
              "PortRangeMin": 22,
              "PortRangeMax": 22,
              "Remark": "zoneB-nat-segment"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "**********/21",
              "DestIP": "",
              "PortRangeMin": 9001,
              "PortRangeMax": 9005,
              "Remark": "master-monitor"
          },
          {
              "EtherType": "IPv4",
              "Direction": "egress",
              "Protocol": "all",
              "SourceIP": "",
              "DestIP": "all",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "all protocol"
          }
      ],
      "CCEServerlessMasterSGRules": [
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "10.0.0.0/8",
              "DestIP": "",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则: 节点间内网通信"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "**********/12",
              "DestIP": "",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则: 节点间内网通信"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "***********/16",
              "DestIP": "",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则: 节点间内网通信"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "**********/21",
              "DestIP": "",
              "PortRangeMin": 10250,
              "PortRangeMax": 10260,
              "Remark": "CCE默认规则: Master监控"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 6443,
              "PortRangeMax": 6443,
              "Remark": "CCE默认规则: APIServer访问"
          },
          {
              "EtherType": "IPv4",
              "Direction": "egress",
              "Protocol": "all",
              "SourceIP": "",
              "DestIP": "all",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则"
          }
      ],
      "CCECustomMasterSGRules": [
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "10.0.0.0/8",
              "DestIP": "",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则: 节点间内网通信"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "**********/12",
              "DestIP": "",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则: 节点间内网通信"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "***********/16",
              "DestIP": "",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则: 节点间内网通信"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 6443,
              "PortRangeMax": 6443,
              "Remark": "CCE默认规则: APIServer访问"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 22,
              "PortRangeMax": 22,
              "Remark": "CCE默认规则: SSH登录"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "**********/21",
              "DestIP": "",
              "PortRangeMin": 9001,
              "PortRangeMax": 9005,
              "Remark": "CCE默认规则: Master监控"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "**********/21",
              "DestIP": "",
              "PortRangeMin": 10250,
              "PortRangeMax": 10252,
              "Remark": "CCE默认规则: Master监控"
          },
          {
              "EtherType": "IPv4",
              "Direction": "egress",
              "Protocol": "all",
              "SourceIP": "",
              "DestIP": "all",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则"
          }
      ],
      "CCENodeIPv4SGRules": [
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "all",
              "SourceIP": "10.0.0.0/8",
              "DestIP": "",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则: 节点间内网通信"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "all",
              "SourceIP": "**********/12",
              "DestIP": "",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则: 节点间内网通信"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "all",
              "SourceIP": "***********/16",
              "DestIP": "",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则: 节点间内网通信"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "all",
              "SourceIP": "************/24",
              "DestIP": "",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则: 与托管Master通信"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 22,
              "PortRangeMax": 22,
              "Remark": "CCE默认规则: SSH登录"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 30000,
              "PortRangeMax": 32768,
              "Remark": "CCE默认规则: 外部用户K8s NodePort默认范围"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "udp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 30000,
              "PortRangeMax": 32768,
              "Remark": "CCE默认规则: 外部用户K8s NodePort默认范围"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "icmp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": -1,
              "PortRangeMax": -1,
              "Remark": "CCE默认规则: Ping命令"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 8000,
              "PortRangeMax": 9000,
              "Remark": "CCE默认规则: 内部用户K8s NodePort默认范围"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "udp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 8000,
              "PortRangeMax": 9000,
              "Remark": "CCE默认规则: 内部用户K8s NodePort默认范围"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "{{.Values.CCEClusterService.Config.RegionNetworkSegment}}",
              "DestIP": "",
              "PortRangeMin": 9101,
              "PortRangeMax": 9101,
              "Remark": "CCE默认规则: Worker监控"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "{{.Values.CCEClusterService.Config.RegionNetworkSegment}}",
              "DestIP": "",
              "PortRangeMin": 9256,
              "PortRangeMax": 9256,
              "Remark": "CCE默认规则: Worker监控"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "{{.Values.CCEClusterService.Config.RegionNetworkSegment}}",
              "DestIP": "",
              "PortRangeMin": 10247,
              "PortRangeMax": 10247,
              "Remark": "CCE默认规则: Worker监控"
          },
          {
              "EtherType": "IPv4",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "{{.Values.CCEClusterService.Config.RegionNetworkSegment}}",
              "DestIP": "",
              "PortRangeMin": 10249,
              "PortRangeMax": 10249,
              "Remark": "CCE默认规则: Worker监控"
          },
          {
              "EtherType": "IPv4",
              "Direction": "egress",
              "Protocol": "all",
              "SourceIP": "",
              "DestIP": "all",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则"
          }
      ],
      "CCENodeIPv6SGRules": [
          {
              "EtherType": "IPv6",
              "Direction": "ingress",
              "Protocol": "all",
              "SourceIP": "fd00::/8",
              "DestIP": "",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则: 容器网段互通, 容器IPv6地址网段"
          },
          {
              "EtherType": "IPv6",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 30000,
              "PortRangeMax": 32768,
              "Remark": "CCE默认规则: 外部用户K8s NodePort默认范围"
          },
          {
              "EtherType": "IPv6",
              "Direction": "ingress",
              "Protocol": "udp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 30000,
              "PortRangeMax": 32768,
              "Remark": "CCE默认规则: 外部用户K8s NodePort默认范围"
          },
          {
              "EtherType": "IPv6",
              "Direction": "ingress",
              "Protocol": "tcp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 8000,
              "PortRangeMax": 9000,
              "Remark": "CCE默认规则: 内部用户K8s NodePort默认范围"
          },
          {
              "EtherType": "IPv6",
              "Direction": "ingress",
              "Protocol": "udp",
              "SourceIP": "all",
              "DestIP": "",
              "PortRangeMin": 8000,
              "PortRangeMax": 9000,
              "Remark": "CCE默认规则: 内部用户K8s NodePort默认范围"
          },
          {
              "EtherType": "IPv6",
              "Direction": "egress",
              "Protocol": "all",
              "SourceIP": "",
              "DestIP": "all",
              "PortRangeMin": 1,
              "PortRangeMax": 65535,
              "Remark": "CCE默认规则: 节点互通"
          }
      ],
      "RegionNetworkSegment": "{{.Values.CCEClusterService.Config.RegionNetworkSegment}}",
      "CCEThanosQuery":"{{.Values.CCEThanos.QueryAddress}}",
      "ConsoleGrafanaAddress": "{{.Values.ConsoleGrafana.Address}}",
      "ConsoleGrafanaUser": "{{.Values.ConsoleGrafana.User}}",
      "ConsoleGrafanaPassword": "{{.Values.ConsoleGrafana.Password}}",
      "EnglishConsoleGrafanaAddress": "{{.Values.EnglishConsoleGrafana.Address}}",
      "EnglishConsoleGrafanaUser": "{{.Values.EnglishConsoleGrafana.User}}",
      "EnglishConsoleGrafanaPassword": "{{.Values.EnglishConsoleGrafana.Password}}",
      "ManagedClusterMinNodeNum": 0,
      "AvailableEBCMachineSpecs": [
        "ehc.lgn5ne.c128m2048.8a800.8re.4d",
        "ehc.lgn5ne.c128m2048.8a100.8re.4d",
        "ehc.lgn5.c128m1024.8a100.8re.4d",
        "ehc.lgn5.c128m2048.8a800.8re.4d",
        "ehc.lnh6t.c192m3072.16a910b.16re.4d",
        "ehc.lnh6t.c192m3072.16a910b.16re.2d",
        "ehc.lnkl6.c192m2048.8p800.4re.4d",
        "ebc.l5.c128m512.4d",
        "ebc.la3.c384m1536.2d",
        "ebc.la2.c256m1024.4d",
        "ebc.la2ne.c256m1024.2d",
        "ebc.l5.c128m1024.1d",
        "ehc.lgn6t.c192m2048.8h800.8re.4d",
        "ehc.lgn5ne.c128m1024.8a800.8re.4d",
        "ehc.lgn6t.c192m3072.8h800.8re.8d",
        "ehc.lgn6t.c192m3072.8h800.8re.4d",
        "ebc.lgn6r.c192m1024.8g4090.2d",
        "bcc.lgn5c.c116m978.8a100.8ib.2d",
        "ehc.lgn7s.c208m1024.8l20.4re.4d",
        "ebc.l5.c128m1024.4d",
        "ebc.incl5.c128m768.2d",
        "ehc.lnkl7t.c208m2048.8p800.4re.4d",
        "ehc.inclnkl7t.c208m2048.8p800.4re.4d",
        "ebc.l5.c128m1024.2d",
        "ebc.la2.c256m1024.2d",
        "ebc.l5me.c128m2048.2d",
        "ebc.lgn5rc.c128m1024.8g4090.1d",
        "ehc.lgn7t.c208m2048.8h20.8re.4d",
        "ebc.l5.c128m512.1d",
        "ebc.la3.c384m1536.1d.c"
      ],
      "SupportCPUThreadMachineSpecs": [
        "ebc.l7.c208m1024.2d",
        "ebc.l7.c208m1024.4d",
        "ehc.lnkl7t.c208m2048.8p800.4re.4d",
        "ebc.la3.c384m768.2d",
        "ebc.la3.c384m1536.2d",
        "ebc.la3.c384m1536.1d.c",
        "ebc.la3.c384m1536.16d",
        "ebc.ca3.c384m768",
        "ebc.d7.c208m1024.12d",
        "ebc.lgna3s.c384m1536.8l20.2d",
        "ebc.gn7s.c208m1024.8l20"
      ],
      "SupportNUMAMachineSpecs": [
        "ebc.l7.c208m1024.2d",
        "ebc.l7.c208m1024.4d",
        "ehc.lnkl7t.c208m2048.8p800.4re.4d",
        "ebc.la3.c384m768.2d",
        "ebc.la3.c384m1536.2d",
        "ebc.la3.c384m1536.1d.c",
        "ebc.la3.c384m1536.16d",
        "ebc.ca3.c384m768",
        "ebc.d7.c208m1024.12d",
        "ebc.lgna3s.c384m1536.8l20.2d",
        "ebc.gn7s.c208m1024.8l20"
      ],
      "AccountBalanceLimit": 100,
      "EventCollectorTags": "{{.Values.CCEMonitorService.Config.EventCollectorTags}}",
      "DisableCCM": {{.Values.DisableCCM}},
      "AINativeAccessKeyID": "{{.Values.CCEClusterService.Config.AINativeAccessKeyID}}",
      "AINativeSecretAccessKey": "{{.Values.CCEClusterService.Config.AINativeSecretAccessKey}}",
      "AINativeBOSBucket": "{{.Values.CCEClusterService.Config.AINativeBOSBucket}}"
    }
{{- end }}
