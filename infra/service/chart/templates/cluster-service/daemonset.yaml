{{- if .Values.CCEClusterServiceEnabled }}
apiVersion: extensions/v1beta1
kind: DaemonSet
metadata:
  namespace: cce-system
  name: cce-cluster-service
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
  labels:
    app: cce
    control-plane: cce-cluster-service
spec:
  updateStrategy:  
    type: RollingUpdate
  selector:
    matchLabels:
      app: cce
      control-plane: cce-cluster-service
  template:
    metadata:
      labels:
        app: cce
        control-plane: cce-cluster-service
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "{{.Values.CCEClusterService.Port}}"
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      {{- if not .Values.CCEImagePluginEnabled }}
      imagePullSecrets:	
      - name: ccr-registry-secret
      - name: ccr-registry-secret-new
      {{- end }}
      initContainers:
        - name: cce-plugin-helm-chart
          image: {{.Values.CCEPluginHelmChart.Image.ImageID}}
          command: ["/bin/sh", "-c", "cp -r /plugin/charts/* /cce/plugin/charts/"]
          imagePullPolicy: Always
          volumeMounts:
            - mountPath: /cce/plugin/charts/
              name: cce-plugin-helm-chart
      containers:
      - args:
        - -app-config=/home/<USER>/cce/cce-cluster-service/conf/app.json
        - -beego-config=/home/<USER>/cce/cce-cluster-service/conf/app.conf
        - -flavor-config=/home/<USER>/cce/cce-cluster-controller/flavor-conf/config.json
        - -log-file=/home/<USER>/cce/cce-cluster-service/logs/cce-cluster-service.log
        - -plugin-config=/home/<USER>/cce/cce-cluster-service/plugin-conf/plugin-config.json
        name: cce-cluster-service
        {{- if .Values.CCEClusterService.Image.ImageDegist}}
        image: {{.Values.CCEClusterService.Image.ImageDegist}}
        {{- else if .Values.CCEClusterService.Image.ImageID }}
        image: {{.Values.CCEClusterService.Image.ImageID}}
        {{- end }}
        imagePullPolicy: Always
        ports:
        - containerPort: {{.Values.CCEClusterService.Port}}
          hostPort: {{.Values.CCEClusterService.Port}}
          name: cluster-service
          protocol: TCP
        resources:
          limits:
            cpu: "8"
            memory: 16000Mi
          requests:
            cpu: 100m
            memory: 200Mi
        livenessProbe: # 健康检查：存活探针
          tcpSocket:
            port: {{.Values.CCEClusterService.Port}}
          initialDelaySeconds: 20
          timeoutSeconds: 5
          periodSeconds: 5
        readinessProbe: # 健康检查：就绪探针
          tcpSocket:
            port: {{.Values.CCEClusterService.Port}}
          initialDelaySeconds: 5
          timeoutSeconds: 5
          periodSeconds: 5
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
          - mountPath: /home/<USER>/cce/cce-cluster-service/conf/
            name: cce-cluster-service-config
          - mountPath: /home/<USER>/cce/cce-cluster-service/plugin-conf/
            name: cce-plugin-config
          - mountPath: /home/<USER>/cce/cce-cluster-controller/flavor-conf/
            name: cce-cluster-flavor-config
          - mountPath: /home/<USER>/cce/cce-cluster-service/logs/
            name: cce-cluster-service-logs
          - mountPath: /root/.kube/
            name: cce-meta-kubeconfig
          - mountPath: /cce/plugin/charts/
            name: cce-plugin-helm-chart
          - mountPath: /home/<USER>/cce/cce-cluster-service/resource-manager/certs/
            name: cce-resource-manager-kafka-ca
      dnsPolicy: {{.Values.HostNetworkDNSPolicy}}
      hostNetwork: true
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: cce-cluster-service-serviceaccount
      terminationGracePeriodSeconds: 10
      volumes:
        - configMap:
            defaultMode: 420
            name: cce-cluster-service-config
          name: cce-cluster-service-config
        - configMap:
            defaultMode: 420
            name: cce-resource-manager-kafka-ca
          name: cce-resource-manager-kafka-ca
        - configMap:
            defaultMode: 420
            name: cce-plugin-config
          name: cce-plugin-config
        - configMap:
            defaultMode: 420
            name: cce-cluster-flavor-config
          name: cce-cluster-flavor-config
        - hostPath:
            path: /home/<USER>/cce/cce-cluster-service/logs/
            type: ""
          name: cce-cluster-service-logs
        - hostPath:
            path: /root/.kube/
            type: ""
          name: cce-meta-kubeconfig
        - emptyDir: {}
          name: cce-plugin-helm-chart
{{- end }}