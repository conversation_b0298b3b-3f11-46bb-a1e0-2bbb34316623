{{- if .Values.CCEGateway.Enabled }}
apiVersion: extensions/v1beta1
kind: DaemonSet
metadata:
  namespace: cce-system
  name: cce-gateway
  annotations:
    release-version: "{{ .Values.ReleaseVersion }}"
  labels:
    app: cce
    control-plane: cce-gateway
spec:
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: cce
      control-plane: cce-gateway
  template:
    metadata:
      labels:
        app: cce
        control-plane: cce-gateway
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "{{.Values.CCEGateway.Port}}"
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      imagePullSecrets:
      - name: ccr-registry-secret
      - name: ccr-registry-secret-new
      containers:
      - name: cce-gateway
        image: {{ .Values.CCEGateway.Image.ImageID }}
        imagePullPolicy: Always
        ports:
        - containerPort: {{ .Values.CCEGateway.Port }}
          hostPort: {{.Values.CCEGateway.Port}}
          name: gateway
          protocol: TCP
        resources:
          limits:
            cpu: "2"
            memory: 8Gi
          requests:
            cpu: 100m
            memory: 200Mi
        livenessProbe: # 健康检查：存活探针
          tcpSocket:
            port: {{ .Values.CCEGateway.Port }}
          initialDelaySeconds: 20
          timeoutSeconds: 5
          periodSeconds: 5
        readinessProbe: # 健康检查：就绪探针
          tcpSocket:
            port: {{ .Values.CCEGateway.Port }}
          initialDelaySeconds: 5
          timeoutSeconds: 5
          periodSeconds: 5
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /home/<USER>/cce/cce-gateway/conf
          name: cce-gateway-config
        - mountPath: /home/<USER>/cce/cce-gateway/log
          name: cce-gateway-log
      dnsPolicy: {{.Values.HostNetworkDNSPolicy}}
      hostNetwork: true
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 10
      volumes:
      - configMap:
          defaultMode: 420
          name: cce-gateway-config
        name: cce-gateway-config
      - hostPath:
          path: /home/<USER>/cce/cce-gateway/log
          type: DirectoryOrCreate
        name: cce-gateway-log
{{- end }}

