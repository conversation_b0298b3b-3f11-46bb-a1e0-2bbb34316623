{{- if .Values.CCEGateway.Enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: cce-system
  name: cce-gateway-config
  annotations:
    release-version: "{{ .Values.ReleaseVersion }}"
data:
  conf.yaml: |
    server:
      address: 0.0.0.0
      port: {{ .Values.CCEGateway.Port }}
    log:
      filename: /home/<USER>/cce/cce-gateway/log/cce-gateway.log
      max_size_in_MB: {{ .Values.CCEGateway.Config.LogMaxSizeInMB }}
      max_backups: {{ .Values.CCEGateway.Config.LogMaxBackups }}
      max_age_in_day: {{ .Values.CCEGateway.Config.LogMaxAgeInDay }}
      compress: false
      utc_time: false
    db:
      conn: {{ .Values.MySQLEndpoint.CCEServiceWrite }}
      debug: {{ .Values.CCEGateway.Config.DBDebugLog }}
      conn_max_lifetime: {{ .Values.CCEGateway.Config.DBConnMaxLifetimeInMin }}
    auth_endpoint: {{ .Values.BCESDKEndpoint.AuthEndpoint }}
    iam_endpoint: {{ .Values.BCESDKEndpoint.IAMEndpoint }}
    iam_agent_endpoint: {{ .Values.BCESDKEndpoint.IAMAgentEndpoint }}
    iam_disaster_tolerance_enabled: {{ .Values.CCEIAMDisasterToleranceEnabled }}
    sts_endpoint: {{ .Values.BCESDKEndpoint.STSEndpoint }}
    serving_endpoint: "{{ .Values.BCESDKEndpoint.OpenCCEGatewayEndpoint }}:80"
    service_account:
      service_name: {{ .Values.CCEServiceAccount.ServiceName }}
      service_password: {{ .Values.CCEServiceAccount.ServicePassword }}
      role_name: {{ .Values.CCEServiceAccount.ServiceRoleName }}
    hpas_service_account:
      service_name: {{ .Values.HPASServiceAccount.PaasApplication }}
      service_account_id: {{ .Values.HPASServiceAccount.ResourceAccountID }}
    cce_service_account:
      service_name: {{ .Values.CCEServiceAccount.ServiceName }}
      service_account_id: {{ .Values.CCEResourceAccount.AccountID }}
    misc:
      skip_token_validate: {{ .Values.CCEGateway.Config.SkipTokenValidate }}
      cluster_reconcile_interval: {{ .Values.CCEGateway.Config.ClusterReconcileIntervalInSec }}
    default_cache_expired_time: {{ .Values.CCEGateway.Config.DefaultCacheExpiredTime }}
    white_list:
      mode: {{ .Values.CCEGateway.Config.WhiteListMode }}


{{- end }}
