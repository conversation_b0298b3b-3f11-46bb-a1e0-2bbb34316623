{{- if .Values.CCEDBSyncController.Enabled }}
apiVersion: extensions/v1beta1
kind: DaemonSet
metadata:
  name: cce-db-sync-controller
  namespace: cce-system
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
  labels:
    app: cce
    control-plane: cce-db-sync-controller
spec:
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: cce
      control-plane: cce-db-sync-controller
  template:
    metadata:
      labels:
        app: cce
        control-plane: cce-db-sync-controller
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9445"
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      {{- if not .Values.CCEImagePluginEnabled }}
      imagePullSecrets:
      - name: ccr-registry-secret
      - name: ccr-registry-secret-new
      {{- end }}
      containers:
        - name: cce-db-sync-controller
          args:
            - -config=/home/<USER>/cce/cce-db-sync-controller/conf/config.json
            - -log-file=/home/<USER>/cce/cce-db-sync-controller/logs/cce-db-sync-controller.log
          image: {{.Values.CCEDBSyncController.Image.ImageID}}
          imagePullPolicy: Always
          resources:
            limits:
              cpu: "4"
              memory: 8000Mi
            requests:
              cpu: 100m
              memory: 20Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /home/<USER>/cce/cce-db-sync-controller/conf/
              name: cce-db-sync-controller-config
            - mountPath: /home/<USER>/cce/cce-db-sync-controller/logs/
              name: cce-db-sync-controller-logs
      dnsPolicy: {{.Values.HostNetworkDNSPolicy}}
      hostNetwork: true
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: cce-db-sync-controller
      terminationGracePeriodSeconds: 10
      volumes:
        - configMap:
            defaultMode: 420
            name: cce-db-sync-controller-config
          name: cce-db-sync-controller-config
        - hostPath:
            path: /home/<USER>/cce/cce-db-sync-controller/logs/
            type: ""
          name: cce-db-sync-controller-logs
{{- end }}