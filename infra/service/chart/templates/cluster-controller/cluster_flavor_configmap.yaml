apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-cluster-flavor-config
  namespace: cce-system
data:
  config.json: |
    {
      "FlavorWhiteList": [
        "l200",
        "l500",
        "l1000",
        "l3000",
        "l5000"
      ],
      "FlavorConfigMap": {
          "l50": {
              "masterPluginConfigList": [{
                      "name": "cce-pro-etcd",
                      "valuesMap": {
                          "LimitCPU": "2",
                          "LimitMemory": "8Gi",
                          "RequestsCPU": "1",
                          "RequestsMemory": "4Gi"
                      }
                  },
                  {
                      "name": "cce-pro-etcd-events",
                      "valuesMap": {
                          "LimitCPU": "500m",
                          "LimitMemory": "2Gi",
                          "RequestsCPU": "100m",
                          "RequestsMemory": "400Mi"
                      }
                  },
                  {
                      "name": "cce-pro-apiserver",
                      "valuesMap": {
                          "LimitCPU": "3",
                          "LimitMemory": "12Gi",
                          "RequestsCPU": "1",
                          "RequestsMemory": "4Gi",
                          "RequestsInflight": 150,
                          "MutatingRequestsInflight": 120,
                          "WatchCacheSize": 1000,
                          "GoawayChance": ".001"
                      }
                  },
                  {
                      "name": "cce-pro-controller-manager",
                      "valuesMap": {
                          "LimitCPU": "1",
                          "LimitMemory": "2Gi",
                          "KubeAPIQPS": 20,
                          "KubeAPIBurst": 30,
                          "RequestsCPU": "500m",
                          "RequestsMemory": "1Gi"
                      }
                  },
                  {
                      "name": "cce-pro-kube-scheduler",
                      "valuesMap": {
                          "LimitCPU": "1",
                          "LimitMemory": "2Gi",
                          "RequestsCPU": "500m",
                          "RequestsMemory": "1Gi"
                      }
                  }
              ],
              "pluginConfigMap": {
                  "cce-lb-controller": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"1\"\r\n  LimitMemory: 4Gi" 
                  },
                  "cce-cloud-node-controller": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"1\"\r\n  LimitMemory: 4Gi" 
                  }
              },
              "resourceLimit": {
                  "nodeNum": 50,
                  "podNum": 1500,
                  "serviceNum": 100,
                  "configMapNum": 300,
                  "secretNum": 300,
                  "pvNum": 250,
                  "pvcNum": 250,
                  "crdNum": 1000
              }
          },
          "l200": {
              "masterPluginConfigList": [{
                      "name": "cce-pro-etcd",
                      "valuesMap": {
                          "LimitCPU": "2",
                          "LimitMemory": "8Gi",
                          "RequestsCPU": "1",
                          "RequestsMemory": "4Gi"
                      }
                  },
                  {
                      "name": "cce-pro-etcd-events",
                      "valuesMap": {
                          "LimitCPU": "500m",
                          "LimitMemory": "2Gi",
                          "RequestsCPU": "100m",
                          "RequestsMemory": "400Mi"
                      }
                  },
                  {
                      "name": "cce-pro-apiserver",
                      "valuesMap": {
                          "LimitCPU": "8",
                          "LimitMemory": "16Gi",
                          "RequestsCPU": "4",
                          "RequestsMemory": "8Gi",
                          "RequestsInflight": 400,
                          "MutatingRequestsInflight": 500,
                          "WatchCacheSize": 1000,
                          "GoawayChance": ".001"
                      }
                  },
                  {
                      "name": "cce-pro-controller-manager",
                      "valuesMap": {
                          "LimitCPU": "2",
                          "LimitMemory": "4Gi",
                          "KubeAPIQPS": 50,
                          "KubeAPIBurst": 100,
                          "RequestsCPU": "500m",
                          "RequestsMemory": "1Gi"
                      }
                  },
                  {
                      "name": "cce-pro-kube-scheduler",
                      "valuesMap": {
                          "LimitCPU": "1",
                          "LimitMemory": "2Gi",
                          "RequestsCPU": "500m",
                          "RequestsMemory": "1Gi"
                      }
                  }
              ],
              "pluginConfigMap": {
                  "cce-lb-controller": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"1\"\r\n  LimitMemory: 4Gi" 
                  },
                  "cce-cloud-node-controller": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"1\"\r\n  LimitMemory: 4Gi" 
                  }
              },
              "resourceLimit": {
                  "nodeNum": 200,
                  "podNum": 6000,
                  "serviceNum": 400,
                  "configMapNum": 1200,
                  "secretNum": 1200,
                  "pvNum": 1000,
                  "pvcNum": 1000,
                  "crdNum": 4000
              }
          },
          "l500":{
              "masterPluginConfigList": [{
                      "name": "cce-pro-etcd",
                      "valuesMap": {
                          "LimitCPU": "3",
                          "LimitMemory": "12Gi",
                          "RequestsCPU": "2",
                          "RequestsMemory": "8Gi"
                      }
                  },
                  {
                      "name": "cce-pro-etcd-events",
                      "valuesMap": {
                          "LimitCPU": "1",
                          "LimitMemory": "4Gi",
                          "RequestsCPU": "500m",
                          "RequestsMemory": "2Gi"
                      }
                  },
                  {
                      "name": "cce-pro-apiserver",
                      "valuesMap": {
                          "LimitCPU": "16",
                          "LimitMemory": "64Gi",
                          "RequestsCPU": "8",
                          "RequestsMemory": "32Gi",
                          "RequestsInflight": 800,
                          "MutatingRequestsInflight": 1000,
                          "WatchCacheSize": 2000,
                          "GoawayChance": ".001"
                      }
                  },
                  {
                      "name": "cce-pro-controller-manager",
                      "valuesMap": {
                          "LimitCPU": "2",
                          "LimitMemory": "4Gi",
                          "KubeAPIQPS": 100,
                          "KubeAPIBurst": 200,
                          "RequestsCPU": "500m",
                          "RequestsMemory": "2Gi"
                      }
                  },
                  {
                      "name": "cce-pro-kube-scheduler",
                      "valuesMap": {
                          "LimitCPU": "1",
                          "LimitMemory": "2Gi",
                          "RequestsCPU": "500m",
                          "RequestsMemory": "1Gi"
                      }
                  }
              ],
              "pluginConfigMap": {
                  "cce-lb-controller": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"1\"\r\n  LimitMemory: 4Gi" 
                  },
                  "cce-pro-external-auditor": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"1\"\r\n  LimitMemory: 4Gi" 
                  },
                  "metrics-server-v2": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"1\"\r\n  LimitMemory: 4Gi\r\n  ScraperLimitCpu: \"500m\"\r\n  ScraperLimitMemory: 1Gi" 
                  },
                  "cce-cloud-node-controller": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"1\"\r\n  LimitMemory: 4Gi" 
                  }
              },
              "resourceLimit": {
                  "nodeNum": 500,
                  "podNum": 15000,
                  "serviceNum": 1000,
                  "configMapNum": 3000,
                  "secretNum": 3000,
                  "pvNum": 2500,
                  "pvcNum": 2500,
                  "crdNum": 10000
              }
          },
          "l1000":{
              "masterPluginConfigList": [{
                      "name": "cce-pro-etcd",
                      "valuesMap": {
                          "LimitCPU": "4",
                          "LimitMemory": "16Gi",
                          "RequestsCPU": "2",
                          "RequestsMemory": "8Gi"
                      }
                  },
                  {
                      "name": "cce-pro-etcd-events",
                      "valuesMap": {
                          "LimitCPU": "2",
                          "LimitMemory": "8Gi",
                          "RequestsCPU": "1",
                          "RequestsMemory": "4Gi"
                      }
                  },
                  {
                      "name": "cce-pro-apiserver",
                      "valuesMap": {
                          "LimitCPU": "32",
                          "LimitMemory": "128Gi",
                          "RequestsCPU": "16",
                          "RequestsMemory": "64Gi",
                          "RequestsInflight": 1200,
                          "MutatingRequestsInflight": 2000,
                          "WatchCacheSize": 2500,
                          "GoawayChance": ".001"
                      }
                  },
                  {
                      "name": "cce-pro-controller-manager",
                      "valuesMap": {
                          "LimitCPU": "4",
                          "LimitMemory": "16Gi",
                          "KubeAPIQPS": 200,
                          "KubeAPIBurst": 400,
                          "RequestsCPU": "1",
                          "RequestsMemory": "4Gi"
                      }
                  },
                  {
                      "name": "cce-pro-kube-scheduler",
                      "valuesMap": {
                          "LimitCPU": "2",
                          "LimitMemory": "8Gi",
                          "RequestsCPU": "500m",
                          "RequestsMemory": "2Gi"
                      }
                  }
              ],
              "pluginConfigMap": {
                  "cce-lb-controller": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"1\"\r\n  LimitMemory: 4Gi" 
                  },
                  "cce-pro-external-auditor": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"1\"\r\n  LimitMemory: 4Gi" 
                  },
                  "metrics-server-v2": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"1\"\r\n  LimitMemory: 4Gi\r\n  ScraperLimitCpu: \"500m\"\r\n  ScraperLimitMemory: 1Gi" 
                  },
                  "cce-cloud-node-controller": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"1\"\r\n  LimitMemory: 4Gi" 
                  }
              },
              "resourceLimit": {
                  "nodeNum": 1000,
                  "podNum": 30000,
                  "serviceNum": 2000,
                  "configMapNum": 6000,
                  "secretNum": 6000,
                  "pvNum": 5000,
                  "pvcNum": 5000,
                  "crdNum": 20000
              }
          },
          "l3000":{
              "masterPluginConfigList": [{
                      "name": "cce-pro-etcd",
                      "valuesMap": {
                          "LimitCPU": "6",
                          "LimitMemory": "24Gi",
                          "RequestsCPU": "3",
                          "ExclusivePlugin": true,
                          "RequestsMemory": "12Gi"
                      }
                  },
                  {
                      "name": "cce-pro-etcd-events",
                      "valuesMap": {
                          "LimitCPU": "4",
                          "LimitMemory": "16Gi",
                          "RequestsCPU": "2",
                          "RequestsMemory": "8Gi"
                      }
                  },
                  {
                      "name": "cce-pro-apiserver",
                      "valuesMap": {
                          "LimitCPU": "64",
                          "LimitMemory": "256Gi",
                          "EnableNetworkApiServer": true,
                          "RequestsCPU": "32",
                          "RequestsMemory": "128Gi",
                          "ExclusivePlugin": true,
                          "RequestsInflight": 1500,
                          "MutatingRequestsInflight": 3000,
                          "WatchCacheSize": 5000,
                          "GoawayChance": ".001"
                      },
                      "valuesYaml": "apiserverList:\n  - name: kube-apiserver-network\n    replicas: 3\n    limitCPU: 64\n    limitMemory: 256Gi\n    requestsCPU: 32\n    requestsMemory: 128Gi\n    requestsInflight: 3000\n    mutatingRequestsInflight: 1500\n    watchCacheSize: 50000\n    goawayChance: .005"
                  },
                  {
                      "name": "cce-pro-controller-manager",
                      "valuesMap": {
                          "LimitCPU": "16",
                          "LimitMemory": "32Gi",
                          "RequestsCPU": "2",
                          "KubeAPIQPS": 600,
                          "KubeAPIBurst": 1200,
                          "RequestsMemory": "8Gi"
                      }
                  },
                  {
                      "name": "cce-pro-kube-scheduler",
                      "valuesMap": {
                          "LimitCPU": "4",
                          "LimitMemory": "16Gi",
                          "RequestsCPU": "2",
                          "RequestsMemory": "8Gi"
                      }
                  }
              ],
              "pluginConfigMap": {
                  "cce-lb-controller": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"2\"\r\n  LimitMemory: 8Gi" 
                  },
                  "cce-pro-external-auditor": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"2\"\r\n  LimitMemory: 8Gi" 
                  },
                  "metrics-server-v2": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"2\"\r\n  LimitMemory: 8Gi\r\n  ScraperLimitCpu: \"500m\"\r\n  ScraperLimitMemory: 1Gi" 
                  },
                  "cce-cloud-node-controller": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"2\"\r\n  LimitMemory: 8Gi" 
                  }
              },
              "resourceLimit": {
                  "nodeNum": 3000,
                  "podNum": 90000,
                  "serviceNum": 6000,
                  "configMapNum": 18000,
                  "secretNum": 18000,
                  "pvNum": 15000,
                  "pvcNum": 15000,
                  "crdNum": 60000
              }
          },
          "L5000": {
              "requiredNodes": [{
                      "instanceType": "BCC",
                      "instanceFlavor": ["bcc.g5.c32m128"],
                      "instanceCount": 5,
                      "minZoneCount": 3
                  },
                  {
                      "instanceType": "BCC",
                      "instanceFlavor": ["bcc.l5.c32m128.1d"],
                      "instanceCount": 6,
                      "minZoneCount": 3
                  }
              ],
              "masterPluginConfigList": [{
                      "name": "cce-pro-etcd",
                      "valuesMap": {
                          "LimitCPU": "50",
                          "LimitMemory": "200Gi",
                          "RequestsCPU": "50",
                          "RequestsMemory": "200Gi",
                          "ExperimentalWatchProgressNotifyInterval": "2s",
                          "QuotaBackendBytes": "18737418240",
                          "ExclusivePlugin": true,
                          "EnableLocalDisk": true
                      }
                  },
                  {
                      "name": "cce-pro-etcd-core",
                      "valuesMap": {
                          "LimitCPU": "50",
                          "LimitMemory": "200Gi",
                          "RequestsCPU": "50",
                          "RequestsMemory": "200Gi",
                          "ExperimentalWatchProgressNotifyInterval": "2s",
                          "QuotaBackendBytes": "18737418240",
                          "ExclusivePlugin": true,
                          "EnableLocalDisk": true
                      }
                  },
                  {
                      "name": "cce-pro-etcd-events",
                      "valuesMap": {
                          "LimitCPU": "50",
                          "LimitMemory": "200Gi",
                          "RequestsCPU": "50",
                          "RequestsMemory": "200Gi",
                          "ExperimentalWatchProgressNotifyInterval": "2s",
                          "QuotaBackendBytes": "18737418240",
                          "ExclusivePlugin": true,
                          "EnableLocalDisk": true
                      }
                  },
                  {
                      "name": "cce-pro-apiserver",
                      "valuesMap": {
                          "Replicas": 5,
                          "EnableNetworkApiServer": true,
                          "LimitCPU": "200",
                          "LimitMemory": "850Gi",
                          "RequestsCPU": "200",
                          "RequestsMemory": "850Gi",
                          "ExclusivePlugin": true,
                          "RequestsInflight": 5000,
                          "MutatingRequestsInflight": 3500,
                          "WatchCacheSize": 5000,
                          "GoawayChance": ".001",
                          "EventTTL": "30m",
                          "EtcdCompactionInterval": "1m"
                      },
                      "valuesYaml": "apiserverList:\n  - name: kube-apiserver-network\n    replicas: 3\n    limitCPU: 200\n    limitMemory: 850Gi\n    requestsCPU: 200\n    requestsMemory: 850Gi\n    requestsInflight: 1500\n    mutatingRequestsInflight: 1500\n    watchCacheSize: 13500\n    goawayChance: .003\n  - name: kube-apiserver-kcm\n    replicas: 3\n    limitCPU: 200\n    limitMemory: 850Gi\n    requestsCPU: 200\n    requestsMemory: 850Gi\n    requestsInflight: 1500\n    mutatingRequestsInflight: 1500\n    watchCacheSize: 13500\n    goawayChance: .003"
                  },      
                  {
                      "name": "cce-pro-controller-manager",
                      "valuesMap": {
                          "Controller":"*,bootstrapsigner,tokencleaner,-endpoint,-endpointslice,-service",
                          "MasterAddress": "kube-apiserver-kcm",
                          "LimitCPU": "60",
                          "LimitMemory": "250Gi",
                          "RequestsCPU": "60",
                          "RequestsMemory": "250Gi",
                          "KubeAPIQPS": 5000,
                          "KubeAPIBurst": 7000,
                          "ConcurrentServiceSyncs": 10,
                          "ConcurrentServiceEndpointSyncs": 50,
                          "ConcurrentEndpointSyncs": 500,
                          "ConcurrentGcSyncs": 5000,
                          "ConcurrentServiceSyncs": 100,
                          "ConcurrentCronJobSyncs": 500,
                          "ConcurrentDeploymentSyncs": 50,
                          "ConcurrentEphemeralvolumeSyncs": 50,
                          "ConcurrentJobSyncs": 500,
                          "ConcurrentRcSyncs": 50,
                          "ConcurrentReplicasetSyncs": 50,
                          "ConcurrentTTLAfterFinishedSyncs": 50
                      }
                  },
                  {
                      "name": "cce-pro-controller-manager-service",
                      "valuesMap": {
                          "Controller":"endpoint,endpointslice,service",
                          "MasterAddress": "kube-apiserver-kcm",
                          "LimitCPU": "60",
                          "LimitMemory": "250Gi",
                          "RequestsCPU": "60",
                          "RequestsMemory": "250Gi",
                          "KubeAPIQPS": 5000,
                          "KubeAPIBurst": 7000,
                          "ConcurrentServiceSyncs": 10,
                          "ConcurrentServiceEndpointSyncs": 50,
                          "ConcurrentEndpointSyncs": 5000,
                          "ConcurrentGcSyncs": 5000,
                          "ConcurrentServiceSyncs": 1000,
                          "ConcurrentCronJobSyncs": 500,
                          "ConcurrentDeploymentSyncs": 50,
                          "ConcurrentEphemeralvolumeSyncs": 50,
                          "ConcurrentJobSyncs": 500,
                          "ConcurrentRcSyncs": 50,
                          "ConcurrentReplicasetSyncs": 50,
                          "ConcurrentTTLAfterFinishedSyncs": 50
                      }
                  },
                  {
                      "name": "cce-pro-kube-scheduler",
                      "valuesMap": {
                          "LimitCPU": "60",
                          "LimitMemory": "250Gi",
                          "RequestsCPU": "30",
                          "RequestsMemory": "125Gi",
                          "KubeAPIQPS": 1000,
                          "KubeAPIBurst": 1500
                      }
                  }
              ],
              "pluginConfigMap": {
                  "cce-lb-controller": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"8\"\r\n  LimitMemory: 24Gi"
                  },
                  "cce-pro-external-auditor": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"8\"\r\n  LimitMemory: 24Gi"
                  },
                  "metrics-server-v2": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"13\"\r\n  LimitMemory: 50Gi\r\n  ScraperLimitCpu: \"13\"\r\n  ScraperLimitMemory: 50Gi" 
                  },
                  "cce-cloud-node-controller": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"13\"\r\n  LimitMemory: 50Gi" 
                  },
                  "cce-node-local-dns": {
                      "valuesYaml": "resource:\r\n  limitCpu: \"13\"\r\n  limitMemory: 50Gi" 
                  },
                  "cilium": {
                      "valuesYaml": "bpf:\r\n  lbMapMax: 165536\r\noperator:\r\n  resources:\r\n    limits:\r\n      cpu: \"13\"\r\n      memory: 50Gi\r\n    requests:\r\n      cpu: \"100m\"\r\n      memory: 128Mi"
                  },
                  "cce-node-feature-discovery": {
                      "valuesYaml": "master:\r\n  resources:\r\n    limits:\r\n      cpu: \"13\"\r\n      memory: 50Gi\r\n    requests:\r\n      cpu: \"100m\"\r\n      memory: 128Mi" 
                  },
                  "kube-state-metrics": {
                      "valuesYaml": "Resource:\r\n  LimitCpu: \"13\"\r\n  LimitMemory: 50Gi" 
                  },
                  "cce-coredns": {
                      "valuesYaml": "resource:\r\n  limitCPU: \"13\"\r\n  limitMemory: 50Gi" 
                  },
                  "cce-cronhpa-controller": {
                      "valuesYaml": "controller:\r\n  resources:\r\n    limits:\r\n      cpu: \"1\"\r\n      memory: \"4Gi\""
                  }
              },
              "resourceLimit": {
                  "nodeNum": 5000,
                  "podNum": 150000,
                  "serviceNum": 10000,
                  "configMapNum": 30000,
                  "secretNum": 30000,
                  "pvNum": 25000,
                  "pvcNum": 25000,
                  "crdNum": 100000
              }
          }
      }
    }