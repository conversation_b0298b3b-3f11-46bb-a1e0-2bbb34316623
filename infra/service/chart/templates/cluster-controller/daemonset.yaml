{{- if .Values.CCEClusterControllerEnabled }}
apiVersion: extensions/v1beta1
kind: DaemonSet
metadata:
  name: cce-cluster-controller
  namespace: cce-system
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
  labels:
    app: cce
    control-plane: cce-cluster-controller
spec:
  updateStrategy:  
    type: RollingUpdate
  selector:
    matchLabels:
      app: cce
      control-plane: cce-cluster-controller
  template:
    metadata:
      labels:
        app: cce
        control-plane: cce-cluster-controller
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9444"
    spec:
      {{- if .Values.NodeSelector }}
      nodeSelector:
        {{.Values.Env}}: deploy
      {{- end }}
      {{- if not .Values.CCEImagePluginEnabled }}
      imagePullSecrets:	
      - name: ccr-registry-secret
      - name: ccr-registry-secret-new
      {{- end }}
      initContainers:
        - name: cce-plugin-helm-chart
          image: {{.Values.CCEPluginHelmChart.Image.ImageID}}
          command: ["/bin/sh", "-c", "cp -r /plugin/charts/* /cce/plugin/charts/"]
          imagePullPolicy: Always
          volumeMounts:
            - mountPath: /cce/plugin/charts/
              name: cce-plugin-helm-chart
      containers:
        - name: cce-cluster-controller
          args:
            - -config=/home/<USER>/cce/cce-cluster-controller/conf/config.json
            - -flavor-config=/home/<USER>/cce/cce-cluster-controller/flavor-conf/config.json
            - -plugin-config=/home/<USER>/cce/cce-cluster-controller/plugin-conf/plugin-config.json
            - -log-file=/home/<USER>/cce/cce-cluster-controller/logs/cce-cluster-controller.log
          image: {{.Values.CCEClusterController.Image.ImageID}}
          imagePullPolicy: Always
          resources:
            limits:
              cpu: "4"
              memory: 16000Mi
            requests:
              cpu: 100m
              memory: 20Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /home/<USER>/cce/cce-cluster-controller/conf/
              name: cce-cluster-controller-config
            - mountPath: /home/<USER>/cce/cce-cluster-controller/flavor-conf/
              name: cce-cluster-flavor-config
            - mountPath: /home/<USER>/cce/cce-cluster-controller/plugin-conf/
              name: cce-plugin-config
            - mountPath: /home/<USER>/cce/cce-cluster-controller/logs/
              name: cce-cluster-controller-logs
            - mountPath: /home/<USER>/cce/packages/
              name: cce-cluster-deployer-packages
            - mountPath: /cce/plugin/charts/
              name: cce-plugin-helm-chart
            - mountPath: /home/<USER>/cce/cce-cluster-controller/resource-manager/certs/
              name: cce-resource-manager-kafka-ca
          env:
            - name: ENABLE_INSTANCE_GROUP_CONTROLLER
              value: "{{.Values.CCEInstanceGroupControllerEnabled}}"
      dnsPolicy: {{.Values.HostNetworkDNSPolicy}}
      hostNetwork: true
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: cce-cluster-controller-serviceaccount
      terminationGracePeriodSeconds: 10
      volumes:
        - configMap:
            defaultMode: 420
            name: cce-cluster-controller-config
          name: cce-cluster-controller-config
        - configMap:
            defaultMode: 420
            name: cce-plugin-config
          name: cce-plugin-config
        - configMap:
            defaultMode: 420
            name: cce-cluster-flavor-config
          name: cce-cluster-flavor-config
        - configMap:
            defaultMode: 420
            name: cce-resource-manager-kafka-ca
          name: cce-resource-manager-kafka-ca
        - hostPath:
            path: /home/<USER>/cce/packages/
            type: ""
          name: cce-cluster-deployer-packages
        - hostPath:
            path: /home/<USER>/cce/cce-cluster-controller/logs/
            type: ""
          name: cce-cluster-controller-logs
        - emptyDir: {}
          name: cce-plugin-helm-chart
{{- end }}