{{- if .Values.CCEIaaSCheckEnabled }}
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  namespace: cce-devops
  name: cce-iaas-check
  annotations:
    release-version: "{{.Values.ReleaseVersion}}"
spec:
  schedule: "0 15 * * *"        # 每天下午3点整运行一次
  failedJobsHistoryLimit: 5      # 保留失败的 Job 数量
  successfulJobsHistoryLimit: 5  # 保留成功的 Job 数量
  jobTemplate:
    spec:
      template:
        spec:
          {{- if .Values.NodeSelector }}
          nodeSelector:
            {{.Values.Env}}: deploy
          {{- end }}
          hostNetwork: true
          {{- if not .Values.CCEImagePluginEnabled }}
          imagePullSecrets:	
          - name: ccr-registry-secret
          - name: ccr-registry-secret-new
          {{- end }}
          containers:
            - name: cce-iaas-check
              image: {{.Values.CCEIaaSCheck.Image.ImageID}}
              imagePullPolicy: Always
              args:
                - -config=/config/conf.json
                - -log-file=/home/<USER>/cce/cce-iaas-check/logs/cce-iaas-check.log
              volumeMounts:
                - name: cce-iaas-check-config
                  mountPath: /config/
                  readOnly: true
                - name: cce-iaas-check-logs
                  mountPath: /home/<USER>/cce/cce-iaas-check/logs/
          volumes:
            - configMap:
                defaultMode: 420
                name: cce-iaas-check-config
              name: cce-iaas-check-config
            - hostPath:
                path: /home/<USER>/cce/cce-iaas-check/logs/
                type: ""
              name: cce-iaas-check-logs
          restartPolicy: OnFailure
  {{- end }}