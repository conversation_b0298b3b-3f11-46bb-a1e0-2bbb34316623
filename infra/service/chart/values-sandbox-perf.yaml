# cce-stack
# values.yaml 包含各地域不同配置

Env: sandbox-perf
Region: sz
ReleaseVersion: RELEASE_VERSION # 发布版本号，cce_stack_control.sh 会进行变量替换
PluginImageRegistry: registry.baidubce.com
IsUnionPayEnv: false # 是否是银商环境
IsPrivateEnv: false # 是否私有化环境
ValidateSubuserPermission: true # 是否开启子用户鉴权
NodeSelector: true #部署是否选择指定
BBCOpenAPI: true # 完全上线之后去除该配置
BCCDeleteOpenAPI: true
ManagedMetaClusterID:
ManagedProEnable: false
EnableDeployByAddOn: true
EnableTag: false
ReplaceMasterBLBFloatingIPByBAEndpoint: true
CheckLccEnvironment: false
InstanceDeployType: ssh
# 是否部署
CCEImageSecretEnabled: true
CCEPluginConfigEnabled: true
CCEClusterCRDEnabled: true
CCEClusterServiceEnabled: true
CCEClusterControllerEnabled: true
CCEMonitorServiceEnabled: true
CCEAPPServiceEnabled: true
CCEK8SEventCollectorEnabled: true
CCEStatusAlertEnabled: false
CCEK8SReportCollectorEnabled: true
CCEClusterSyncEnabled: true
CCEInstanceGroupControllerEnabled: true
CCEMetaClusterLogCollectorEnabled: false
CCEOIDCProviderEnabled: true
CCEDevopsMySQLEnabled: true
CCEIaaSCheckEnabled: true
CCEInstanceNodeController: true
CCEServiceGroupConsoleEnabled: false
CCETaskController: true
CCEAIServiceEnabled: true
CCEMasterLBControllerEnabled: false
# IAM容灾
CCEIAMDisasterToleranceEnabled: false
# Giano Token
GianoToken: bbb90e055a69bf7bd21e0b6137031c6c
# 健康检查
CCEClusterHealthCheckEnabled: true
CCEInstanceHealthCheckEnabled: true
# 配置管理
CCEConfigRCLocalEnabled: true
CCEConfigETCCrontabEnabled: true
# Palo 采集任务
CCEPaloCollectorTestResultEnabled: false
CCEPaloCollectorStatisticsEnabled: false
# Workflow
CCEWorkflowControllerEnabled: true
# 监控采集
NodeExporterEnabled: false
# 监控服务
CCEThanosEnabled: false
CCEThanosPrometheusEnabled: false # 设置为 false, 避免升级修改 STS 副本数
CCEMasterServiceDiscoveryEnabled: false
# 镜像拉取
CCEImagePluginEnabled: false
HostNetworkDNSPolicy: ClusterFirstWithHostNet
# EIP 购买类型
EIPPurchaseType:
# 默认EIP计费类型 用于部署 cce-ingress-controller 与 cce-lb-controller 部署时设置启动参数
EIPBillingMethod:
# 是否关闭新建集群的 CCM 并使用容器化组件作为替代
DisableCCM: true
# CCE 服务号
CCEServiceAccount:
  ServiceName: cce
  ServiceRoleName: BceServiceRole_SERVICE_CCE
  ServicePassword: nHrpnEDTIOonHC1QTOLGDOSjKbqfwKKy
  AccessKeyID: d17d084b99184c0ab89201a43c07b026
  SecretAccessKey: 20bce0d56b944e84a32f83350828b5ae
  ServiceIAMPermissionCheckName: bce:cce
# CCE 资源账号
CCEResourceAccount:
  AccountID: AccountID
  AccessKeyID: AccessKeyID
  SecretAccessKey: SecretAccessKey
# HPAS 服务账号
HPASServiceAccount:
  PaasApplication: HPAS
  ResourceAccountID: 2bd44531802c479296e49d84affb5d49
# MySQL Endpoint
MySQLEndpoint:
  CCEServiceWrite: cce_qa:aoqioupcEqw!@tcp(***********:6747)/cce_preonline?charset=utf8&parseTime=True&loc=Local
  CaaSManagerWrite: cce_qa:aoqioupcEqw!@tcp(***********:6747)/cce_preonline?charset=utf8&parseTime=True&loc=Local
  MonitorServiceWrite: cce_qa:aoqioupcEqw!@tcp(***********:6747)/cce_preonline?charset=utf8&parseTime=True&loc=Local
  E2ETestWrite: cce_qa:aoqioupcEqw!@tcp(***********:6747)/cce_preonline?charset=utf8&parseTime=True&loc=Local
# BCE SDK Endpoint
BCESDKEndpoint:
  AuthEndpoint: 10.133.65.14:8400 #暂时用沙盒的
  UserSettingEndpoint: plat-user-config.internal-qasandbox-new.baidu-int.com:8690/v1
  QuotaCenterEndpoint: plat-user-config.internal-qasandbox-new.baidu-int.com:8690
  STSEndpoint: sts.sz.internal-qasandbox.baidu-int.com:8586/v1
  IAMEndpoint: iam.sz.internal-qasandbox.baidu-int.com:80
  IAMAgentEndpoint: gzdt-yl32-sandbox00-6271.gzdt.baidu.com:8236
  # 检查结尾是否需要 v1
  VPCEndpoint: bcc.sz.qasandbox.baidu-int.com
  VPCInternalEndpoint: logical-vpc.sz.internal-qasandbox.baidu-int.com
  ImageEndpoint: bcc.sz.qasandbox.baidu-int.com
  ImageLogicEndpoint: logic-bcc.internal-qasandbox-sz.baidu-int.com
  ZoneEndpoint: logic-zone.internal-qasandbox-new.baidu-int.com:8795
  BCCEndpoint: bcc.sz.qasandbox.baidu-int.com
  BBCEndpoint: bbc.sz.qasandbox.baidu-int.com
  BCCLogicEndpoint: logic-bbc.sz.internal-qasandbox.baidu-int.com
  BCCLogicalEndpoint: *************:20001
  BCCNeutronEndpoint: ************:9696
  BLBLogicEndpoint: logical-blb.sz.internal-qasandbox.baidu-int.com
  BLBInternalEndpoint: blb.sz.internal-qasandbox.baidu-int.com:80
  BLBOpenAPIEndpoint: blb.sz.qasandbox.baidu-int.com
  AppBLBInternalAPIEndpoint: blb.sz.internal-qasandbox.baidu-int.com:80/open-api/v1
  AppBLBOpenAPIEndpoint: blb.sz.qasandbox.baidu-int.com/v1
  EIPInternalEndpoint: logical-eip.sz.internal-qasandbox.baidu-int.com
  EIPOpenAPIEndpoint: eip.sz.qasandbox.baidu-int.com
  CCEServiceInternalEndpoint: ************:8692
  CCEServicePluginEndpoint: ************:8692
  CCEV2Endpoint: ************:8792/api/cce/service/v2
  CCEMonitorEndpoint: ************:8082
  KMSEndpoint: https://bkm.bj.baidubce.com #sand-box-perf暂无
  CCEGatewayEndpoint: ************:8299
  OpenCCEGatewayEndpoint: ************:8299
  BCILogicEndpoint: bci.bj.baidubce.com #sand-box-perf暂无
  ETCDManagerEndpoint: ************:8380
  CCEOIDCProviderEndpoint: ************:8847
  BECEndpoint: gwgp-whunf2p4fd9.i.gateway-test.baidu-int.com
  IAMForBECEndpoint: iam.hkg.internal-qasandbox.baidu-int.com:80/v3 #待换成沙盒
  FinanceEndpoint: console-finance-perf.baidu-int.com
  CPromOpenEndpoint: http://*************:18794/api/v1/cprom
  CPromEndpoint: http://*************:18794/api/v1/cprom # 暂用 gztest
  CPromV2Endpoint: *************:18794
  QualifyEndpoint: bjyz-y22-sandbox007.bjyz.baidu.com:8291
  TagEndpoint: logic-tag.internal-qasandbox-new.baidu-int.com:8777/v1
  LogicTagEndpoint: bjhw-sys-rpm8059.bjhw:8777
  BCMEndpoint: 10.6.72.14:80
  OrderEndpoint: order.internal-qasandbox.bce-internal.baidu-int.com
  APPServiceEndpoint: ************:8009/api/v1
  CRMEndpoint: gzns-store-sandbox088.gzns:8531/v1 #暂无
  CCREndpoint: 10.31.88.150:8997 #暂用沙盒服务
  NeutronEndpoint: ************:9696
  BOSEndpoint: bj-bos-sandbox.baidu-int.com:80
  BLSInternalEndpoint: bls.gz.baidubce.com:8085 #暂无
  BLSServerEndpoint: https://bls.gz.baidubce.com:8185
  BLSEndpoint: bls-log.gz.baidubce.com
  ECCREndpoint: 10.31.88.150:8997 #暂用沙盒个人版服务
  BusEndpoint: register.internal-qasandbox.baidu-int.com:8985
  CCEV2EndpointForTag: ************:8792
  LccEndpoint:
  ResourceManagerEndpoint: res-manager.bce-console.baidu-int.com
  HPASInternalEndpoint: logic-hpas.internal-qasandbox.baidu-int.com
  HPASEndpoint: hpas.bj.qasandbox.baidu-int.com
  GianoEndpoint: http://giano-cloud-sandbox.baidu-int.com:8889
# CCE Cluster Service
CCEClusterService:
  Port: 8792
  Image:
    ImageID: cce-cluster-service-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    Handler: default
    RegionNetworkSegment: *********/16
    DefaultMasterConfig:
      InstanceType: N3
      FlavorSmallCPU: 2
      FlavorSmallMEM: 4
      FlavorMediumCPU: 4
      FlavorMediumMEM: 8
      FlavorLargeCPU: 16
      FlavorLargeMEM: 32
      RootDiskType: hp1
      RootDiskSize: 100
      ImageType: System
      ImageName: 7.3 x86_64 (64bit)
      OSType: linux
      OSName: CentOS
      OSArch: x86_64 (64bit)
      OSVersion: 7.3
    ServerlessAvailable: true
    DefaultServerlessMasterConfig: {}
    SkipUpdateInstanceGroupModel: false
    EnableEdgeHubInCloudEdgeCluster: false
    EnableUpgradeWorkflow: true
AINativeAccessKeyID: ALTAKuNsep2QOEP1LmEFM8ObQP
AINativeSecretAccessKey: 352712a971df46e981a1d02ec78942cc
AINativeGlobalBOSEndpoint: "bj.bcebos.com"
AINativeGlobalBucket: cce-ai-native-bj
AINativeBOSBucket: cce-ai-native-gztest
AIDemoClusterID: cce-zc1xn31g
AIDemoClusterUser: eca97e148cb74e9683d7b7240829d1ff
# CCE 插件依赖 Helm Chart
CCEPluginHelmChart:
  Image:
    ImageID: cce-plugin-helm-chart-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Cluster Controller
CCEClusterController:
  Image:
    ImageID: cce-cluster-controller-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    CheckK8SNodeNameDuplicatedInDB: true
    ClusterConcurrency: 10
    ClusterRequeueAfterSeconds: 30
    InstanceConcurrency: 400
    InstanceRequeueAfterSeconds: 30
    TaskConcurrency: 40
    InstanceGroupMaxConcurrency: 5
    UseTaskToCreateMachines: true
    UseTaskToRemoveInstances: true
    SkipInstanceGroupModel: true
    TaskCleanerDetectionPeriod: 600
    TaskCleanerProtectionDuration: 600
    InstanceHandlerAnnotation: default
    ClusterHandlerAnnotation: default
    InstanceGroupHandlerAnnotation: default
    TaskHandlerAnnotation: default
    ManagedProSCName: enhanced-ssd-pl1
    BLBLayer4ClusterID:
    KubeStateMetricFTPAddress: http://baidu-container-gztest.gz.bcebos.com/packages/kube-state-metrics/kube-state-metrics
    TelegrafFTPAddress: http://baidu-container-gztest.gz.bcebos.com/packages/telegraf/telegraf
    LogbeatFTPAddress: http://baidu-container-gztest.gz.bcebos.com/packages/logbeat/logbeat
    BLSAgentToken: "todo"
    CCMPath: http://baidu-container-gztest.gz.bcebos.com/packages/cloudprovider/cce-cloud-controller-manager
    AuditerPath: http://baidu-container-gztest.gz.bcebos.com/packages/auditer/kube-external-auditer
    KMSPath: http://baidu-container-gztest.gz.bcebos.com/packages/kms-plugin/v1.0.0/k8s-cloudkms-plugin
    ExtenderSchedulerPath: http://baidu-container-gztest.gz.bcebos.com/packages/gpu-extender/nvidia-share-extender-scheduler
    KubeBinPath_1_11_1: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.11.1.tar.gz
    KubeBinPath_1_11_5: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.11.5.tar.gz
    KubeBinPath_1_13_4: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.13.4.tar.gz
    KubeBinPath_1_13_10: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.13.10.tar.gz
    KubeBinPath_1_14_9: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.14.9.tar.gz
    KubeBinPath_1_16_3: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.16.3.tar.gz
    KubeBinPath_1_16_8: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.16.8.tar.gz
    KubeBinPath_1_17_17: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.17.17.tar.gz
    KubeBinPath_1_18_9: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.18.9.tar.gz
    KubeBinPath_1_18_9_BilibiliMixprotocols: http://baidu-container.bj.bcebos.com/kube/kubebins-1.18.9-bilibili-mixprotocols.tar.gz
    KubeBinPath_1_20_8: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.20.8-20240601-patch-nokmem.tar.gz
    KubeBinPath_1_21_14: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.21.14.tar.gz
    KubeBinPath_1_20_8_containerd: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.20.8-containerd-nokmem.tar.gz
    KubeBinPath_1_22_5_containerd: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.22.5-containerd.tar.gz
    KubeBinPath_1_24_4_containerd: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.24.4-containerd.tar.gz
    KubeBinPath_1_22_5: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.22.5-20231121-patch.tar.gz
    KubeBinPath_1_24_4: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.24.4.tar.gz
    KubeBinPath_1_26_9: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.26.9.tar.gz
    ContainerdBinPath_1_5_4: http://baidu-container-gztest.gz.bcebos.com/runtime/containerd/cri-containerd-cni-1.5.4-linux-amd64.tar.gz
    ContainerdBinPath_1_6_8: http://baidu-container-gztest.gz.bcebos.com/runtime/containerd/cri-containerd-cni-1.6.8-linux-amd64.tar.gz
    ContainerdBinPath_1_6_20: http://baidu-container-gztest.gz.bcebos.com/runtime/containerd/cri-containerd-cni-1.6.20-linux-amd64.tar.gz
    ContainerdBinPath_1_6_24: http://baidu-container-gztest.gz.bcebos.com/runtime/containerd/cri-containerd-cni-1.6.24-linux-amd64.tar.gz
    ContainerdBinPath_1_6_28: http://baidu-container-gztest.gz.bcebos.com/runtime/containerd/cri-containerd-cni-1.6.28-linux-amd64.tar.gz
    ContainerdBinPath_1_6_28_arm64: http://baidu-container-gztest.gz.bcebos.com/runtime/containerd/cri-containerd-cni-1.6.28-linux-arm64.tar.gz
    ContainerdBinPath_1_6_28_Rootfs: http://baidu-container-gztest.gz.bcebos.com/runtime/containerd/cri-containerd-cni-1.6.28-linux-amd64-rootfs.tar.gz
    ContainerdBinPath_1_6_36: http://baidu-container-gztest.gz.bcebos.com/runtime/containerd/cri-containerd-cni-1.6.36-linux-amd64.tar.gz
    ContainerdBinPath_1_6_36_Rootfs: http://baidu-container-gztest.gz.bcebos.com/runtime/containerd/cri-containerd-cni-1.6.36-linux-amd64-rootfs.tar.gz
    ContainerdBinPath_1_7_13: http://baidu-container-gztest.gz.bcebos.com/runtime/containerd/cri-containerd-cni-1.7.13-linux-amd64.tar.gz
    ContainerdBinPath_1_7_25: http://baidu-container-gztest.gz.bcebos.com/runtime/containerd/cri-containerd-cni-1.7.25-linux-amd64.tar.gz
    DockerBinPath_20_10_5: http://baidu-container-gztest.gz.bcebos.com/runtime/docker/docker-20.10.5.tgz
    DockerBinPath_20_10_24: http://baidu-container-gztest.gz.bcebos.com/runtime/docker/docker-20.10.24.tgz
    DockerBinPath_20_10_24_arm64: http://baidu-container-gztest.gz.bcebos.com/runtime/docker/docker-20.10.24-arm64.tgz
    NvidiaContainerToolkitPath: http://baidu-container-gztest.gz.bcebos.com/packages/nvidia-packages/nvidia-container-toolkit-25-04-29.tar.gz
    PauseImage_2_0: registry.baidubce.com/public/pause:2.0
    PauseImage_3_1: registry.baidubce.com/cce-public/pause:3.1
    PauseImage_3_2: registry.baidubce.com/cce-public/pause:3.2
    PauseImage_3_1_arm64: registry.baidubce.com/cce-public/pause-arm64:3.1
    KubeAPIServer_1_18_9: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.18.9
    KubeControllerManager_1_18_9: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.18.9
    KubeScheduler_1_18_9: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.18.9
    KubeAPIServer_1_16_8: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.16.8
    KubeControllerManager_1_16_8: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.16.8
    KubeScheduler_1_16_8: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.16.8
    KubeAPIServer_1_17_17: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.21.14
    KubeControllerManager_1_17_17: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.21.14
    KubeScheduler_1_17_17: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.21.14
    KubeAPIServer_1_20_8: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.20.8
    KubeControllerManager_1_20_8: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.20.8-20231120-patch
    KubeScheduler_1_20_8: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.20.8
    KubeAPIServer_1_20_8_arm64: registry.baidubce.com/cce-public/kube-apiserver-arm64:v1.20.8
    KubeControllerManager_1_20_8_arm64: registry.baidubce.com/cce-public/kube-controller-manager-arm64:v1.20.8
    KubeScheduler_1_20_8_arm64: registry.baidubce.com/cce-public/kube-scheduler-arm64:v1.20.8
    KubeAPIServer_1_21_14: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.21.14
    KubeControllerManager_1_21_14: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.21.14
    KubeScheduler_1_21_14: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.21.14
    KubeAPIServer_1_22_5: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.22.5
    KubeControllerManager_1_22_5: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.22.5-20231121-patch
    KubeScheduler_1_22_5: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.22.5
    KubeAPIServer_1_24_4: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.24.4
    KubeControllerManager_1_24_4: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.24.4
    KubeScheduler_1_24_4: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.24.4
    KubeAPIServer_1_26_9: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.26.9
    KubeControllerManager_1_26_9: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.26.9
    KubeScheduler_1_26_9: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.26.9
    KubeAPIServer_1_18_9_BilibiliMixprotocols: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.18.9-bilibili-mixprotocols
    KubeScheduler_1_18_9_BilibiliMixprotocols: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.18.9-bilibili-mixprotocols
    KubeControllerManager_1_18_9_BilibiliMixprotocols: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.18.9-bilibili-mixprotocols
    KubeExternalAuditorImage: registry.baidubce.com/cce-plugin-pro/kube-external-auditor:v1.1.2
    ManagedProKubeAPIServer_1_16_8: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.16.8
    ManagedProKubeAPIServer_1_17_17: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.17.17
    ManagedProKubeAPIServer_1_18_9: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.18.9
    ManagedProKubeAPIServer_1_20_8: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.20.8
    ManagedProKubeAPIServer_1_21_14: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.21.14
    ManagedProKubeAPIServer_1_22_5: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.22.5
    ManagedProKubeAPIServer_1_24_4: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.24.4
    ManagedProKubeAPIServer_1_26_9: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.26.9
    ManagedProKubeControllerManager_1_16_8: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.16.8
    ManagedProKubeControllerManager_1_17_17: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.17.17
    ManagedProKubeControllerManager_1_18_9: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.18.9
    ManagedProKubeControllerManager_1_20_8: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.20.8-20231120-patch
    ManagedProKubeControllerManager_1_21_14: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.21.14
    ManagedProKubeControllerManager_1_22_5: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.22.5-20231121-patch
    ManagedProKubeControllerManager_1_24_4: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.24.4
    ManagedProKubeControllerManager_1_26_9: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.26.9
    ManagedProKubeScheduler_1_16_8: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.16.8
    ManagedProKubeScheduler_1_17_17: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.17.17
    ManagedProKubeScheduler_1_18_9: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.18.9
    ManagedProKubeScheduler_1_20_8: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.20.8
    ManagedProKubeScheduler_1_21_14: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.21.14
    ManagedProKubeScheduler_1_22_5: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.22.5
    ManagedProKubeScheduler_1_24_4: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.24.4
    ManagedProKubeScheduler_1_26_9: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.26.9
    ManagedProETCDImage: registry.baidubce.com/cce-managed-pro/etcd:3.5.9
    ManagedProKubeInitImage: registry.baidubce.com/cce-managed-pro/cce-kube-master-init:v1.0.1
    ManagedProDebugerImage: registry.baidubce.com/cce-managed-pro/cce-debuger:v1.1.0
    EdgeHub: registry.baidubce.com/cce-edge/ote_edgehub:*******
    CCEAgentImage: registry.baidubce.com/cce-plugin-pro/cce-agent:20250410
    EIPPurchaseType:
    KubeBinPath:
      1.28.8: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.28.8.tar.gz
      1.30.1: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.30.1.tar.gz
      1.31.1: http://baidu-container-gztest.gz.bcebos.com/kube/kubebins-1.31.1.tar.gz
    KubeAPIServer:
      1.28.8: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.28.8
      1.30.1: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.30.1
      1.31.1: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.31.1
    KubeControllerManager:
      1.28.8: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.28.8
      1.30.1: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.30.1
      1.31.1: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.31.1
    KubeScheduler:
      1.28.8: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.28.8
      1.30.1: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.30.1
      1.31.1: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.31.1
    ManagedProKubeAPIServer:
      1.28.8: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.28.8
      1.30.1: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.30.1
      1.31.1: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.31.1
    ManagedProKubeControllerManager:
      1.28.8: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.28.8
      1.30.1: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.30.1
      1.31.1: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.31.1
    ManagedProKubeScheduler:
      1.28.8: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.28.8
      1.30.1: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.30.1
      1.31.1: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.31.1
# CCE Monitor Service
CCEMonitorService:
  Port: 8082
  Image:
    ImageID: cce-monitor-service-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    AuthEndpoint: http://**************:8400/
    AuditHost: "not-available"
    AuditEndpoint: "not-available"
    AuditConfigmapEndpoint: "not-available"
    BOSEndpoint: hkg-bos-sandbox.baidu-int.com
    BOSS3Endpoint: http://s3.%s.bcebos.com
    BOSBucket: cce-backup-online-bj
    JPaaSAccessKeyID: ALTAKvhzrJjp6AnLBFZ6yfn2MX
    JPaaSSecretAccessKey: 878956c054f54684ad0d7e2d96c34486
    ElasticEndpoint: http://*************:8200/
    ElasticUserName: elastic
    ElasticPassword: changeme
    EventCollectorTags: node01
    ThanosQueryEndpoint: http://************:8990
    CCEAlertWebHookURL: http://cce-monitor-webhook.baidubce.com
  Endpoints:
    - region: "bj"
      endpoint: "http://monitor.cce-bj.baidu-int.com"
      iamEndpoint: "iam.bj.bce-internal.baidu.com"
      stsEndpoint: "sts.bj.iam.sdns.baidu.com:8586/v1"
    - region: "gz"
      endpoint: "http://monitor.cce-gz.baidu-int.com"
      iamEndpoint: "iam.gz.bce-internal.baidu.com"
      stsEndpoint: "sts.gz.iam.sdns.baidu.com:8586/v1"
    - region: "hkg"
      endpoint: "http://10.70.8.24:8081"
      iamEndpoint: "iam.hkg.bce.baidu-int.com"
      stsEndpoint: "sts.hkg.bce.baidu-int.com:8586/v1"
    - region: "bd"
      endpoint: "http://monitor.cce-bd.baidu-int.com"
      iamEndpoint: "iam.bdbl.bce.baidu-int.com"
      stsEndpoint: "sts.bdbl.bce.baidu-int.com:8586/v1"
    - region: "su"
      endpoint: "http://monitor.cce-sz.baidu-int.com"
      iamEndpoint: "iam.su.bce-internal.baidu.com"
      stsEndpoint: "sts.su.iam.sdns.baidu.com:8586/v1"
    - region: "fwh"
      endpoint: "http://10.70.16.76:8081"
      iamEndpoint: "iam.fwh.bce.baidu-int.com"
      stsEndpoint: "sts.fwh.bce.baidu-int.com:8586/v1"
    - region: "nj"
      endpoint: "http://10.70.40.30:8081"
      iamEndpoint: "iam.nj.bce.baidu-int.com"
      stsEndpoint: "sts.nj.bce.baidu-int.com/v1"
    - region: "yq"
      endpoint: "http://10.6.104.15:8081"
      iamEndpoint: "iam.yq.bce-internal.sdns.baidu.com"
      stsEndpoint: "sts.yq.iam.sdns.baidu.com:8586/v1"
    - region: "cd"
      endpoint: "http://monitor.cce-cd.baidu-int.com"
      iamEndpoint: "iam.cd.bce-internal.sdns.baidu.com"
      stsEndpoint: "sts.cd.iam.sdns.baidu.com:8586/v1"
    - region: "edge"
      endpoint: "http://monitor.cce-egsz.baidu-int.com"
      iamEndpoint: "iam.su.bce-internal.baidu.com"
      stsEndpoint: "sts.su.iam.sdns.baidu.com:8586/v1"
# CCE APP Service
CCEAPPService:
  Port: 8009
  Image:
    ImageID: cce-app-service-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    AuthEndpoint: http://10.133.65.14:8400/
    RedisEndponit: redis://:<EMAIL>:8379/0 # 用于 webshell
    CCEIngressControllerImageID: registry.baidubce.com/cce-plugin-pro/cce-ingress-controller:1.29.5
# CCE Cluster Sync
CCEClusterSync:
  Image:
    ImageID: cce-cluster-sync-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    CronPeriod: 600
# CCE K8S Event collector
CCEK8SEventCollector:
  Image:
    ImageID: k8s-event-collector-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    ElasticSearchEndpoint: http://*************:8200?sniff=false&cluster_id=%s&index=%s&esUserName=elastic&esUserSecret=changeme&esVersion=6
    K8SEventTag: node01 # 该k8s-event-collector实例需要收集的集群标识
    SinkType: elasticsearch # 采集输出端
    CyclePeriod: 120 # 定期轮询集群是否开启事件采集的时间间隔， 单位是秒， 默认 2*60   = 120 s
    IntervalTime: 168 # 定期清理过期es index 时间间隔，      单位是小时，默认 7 * 24 = 168 小时
    Replicas: 1 # 部署的采集器的副本数
# CCE MetaCluster log collector
CCEMetaClusterLogCollector:
  Index: preonline
  Image:
    ImageID: registry.baidubce.com/cce-plugin-pro/fluent-bit:1.5-debug
  Config:
    ElasticSearchHost: *************
    ElasticSearchPort: 8200
    ElasticSearchUser: elastic
    ElasticSearchPassword: changeme
# CCE K8S report collector
CCEK8SReportCollector:
  Image:
    ImageID: k8s-report-collector-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Cluster Status Alert
CCEStatusAlert:
  Image:
    ImageID: cce-status-alert-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Helm Service
CCEHelmService:
  Enabled: true
  Port: 8085
  Image:
    ImageID: cce-helm-service-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    PublicRepoEndpoint: http://**************:8098 # public chartmuseum
    PrivateRepoPrefix: http://**************:8099 # private chartmuseum
    ChartPackageMaxUploadSizeInMB: 1 # 限制用户上传chart package最大1M
    ChartsUploadQuota: 0 # 限制私有仓库charts数目, 0代表不限制
    KubectlBin: kubectl-1_11
    HelmV2Bin: helm-2.12.3
    HelmV3Bin: helm-3.2.4
    TillerImage: registry.baidubce.com/jpaas-public/tiller:v2.12.3
    SocatInstallerImage: registry.baidubce.com/jpaas-public/socat-installer:alpine-3.5
# CCE Gateway
CCEGateway:
  Enabled: true
  Port: 8299
  Image:
    ImageID: cce-gateway-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    DBDebugLog: false
    DBConnMaxLifetimeInMin: 8
    SkipTokenValidate: false
    ClusterReconcileIntervalInSec: 600
    LogMaxBackups: 128
    LogMaxSizeInMB: 64
    LogMaxAgeInDay: 30
# CCE OIDC Provider
CCEOIDCProvider:
  Port: 8847
  Image:
    ImageID: cce-oidc-provider-IMAGEID
    ImageDegist:
  TLS:
    CA:
    Crt:
    Key:
  Config:
    Issuer: https://************:8792/oidc
# CCE ETCD Manager
CCEETCDManager:
  Enabled: false
CCEPaloCollector:
  Image:
    ImageID: cce-palo-collector-IMAGEID
# CCE Config Manager
CCEConfigManager:
  Image:
    ImageID: cce-config-manager-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Virtual Kubelet for bci pod
CCEVirtualKubelet:
  Enabled: false
  Image: registry.baidubce.com/cce-plugin-pro/bci-virtual-kubelet:2020.10.22.1
  Port: 10450 # 指定10250以外的未使用端口，10250会和机器上kubelet冲突
  MetricsPort: 10355 # 指定10255以外的未使用端口，10255会和机器上kubelet冲突
  LogMaxBackups: 20 # 20*256MB
  NodeName: "cce-virtual-kubelet"
  ChargeApplication: "CCE" # 转移计费到资源账号
  ChargeAccessKey: "e08e132e64d246f596ad5be55cbf37d8"
  ChargeSecretKey: "6047e7d886ee49849e3d82df9259f127"
# CCE webhook
CCEValidateWebhook:
  Enabled: true
  Image:
    ImageID: cce-validate-webhook-IMAGEID
# CCE IaaS Check
CCEIaaSCheck:
  Image:
    ImageID: cce-iaas-check-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Cluster Health Check
CCEClusterHealthCheck:
  Image:
    ImageID: cce-health-check-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Instance Health Check
CCEInstanceHealthCheck:
  Image:
    ImageID: cce-health-check-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Thanos
CCEThanos:
  Image:
    GrafanaProvisioningImageID: cce-grafana-provisioning-IMAGEID
  CCEServiceAccountBOSKey: e08e132e64d246f596ad5be55cbf37d8
  CCEServiceAccountBOSSecret: 6047e7d886ee49849e3d82df9259f127
  QueryAddress: http://**************:8481/select/0/prometheus
# CCE Master Service Discovery
CCEMasterServiceDiscovery:
  Image:
    ImageID: cce-master-service-discovery-IMAGEID # cce_stack_control.sh 会进行变量替换
# bgw vip sync
bgwService:
  enabled: true
  csBgwSvc:
    name: cce-cluster-service-bgwsvc
    vip: ************
    port: 8792
    containerName: cce-cluster-service
    servicePortName: cluster-service
    protocol: TCP
    creator: <EMAIL>
    syncBgwByExistence: false
  cgBgwSvc:
    name: cce-gateway-bgwsvc
    vip: ************
    port: 8299
    containerName: cce-gateway
    servicePortName: gateway
    protocol: TCP
    creator: <EMAIL>
    syncBgwByExistence: false
  opBgwSvc:
    name: cce-oidc-provider-bgwsvc
    vip: ************
    port: 8847
    containerName: cce-oidc-provider
    servicePortName: https
    protocol: TCP
    creator: <EMAIL>
    syncBgwByExistence: false
  asBgwSvc:
    name: cce-app-service-bgwsvc
    vip: ************
    port: 8009
    containerName: cce-app-service
    servicePortName: cce-app-service
    protocol: TCP
    creator: <EMAIL>
    syncBgwByExistence: false
  emBgwSvc:
    name: cce-etcd-manager-bgwsvc
    vip: ************
    port: 8380
    containerName: cce-etcd-manager
    servicePortName: etcd-manager
    protocol: TCP
    creator: <EMAIL>
    syncBgwByExistence: false
  hsBgwSvc:
    name: cce-helm-service-bgwsvc
    vip: ************
    port: 8085
    containerName: cce-helm-service
    servicePortName: helm-service
    protocol: TCP
    creator: <EMAIL>
    syncBgwByExistence: false
  sgcBgwSvc:
    name: service-group-console-bgwsvc
    vip: ************
    port: 8895
    containerName: service-group-console
    servicePortName: service-group
    protocol: TCP
    creator: <EMAIL>
  msBgwSvc:
    name: cce-monitor-service-bgwsvc
    vip: ************
    port: 8082
    containerName: cce-monitor-service
    servicePortName: monitor-service
    protocol: TCP
    creator: <EMAIL>
  aiBgwSvc:
    name: cce-ai-service-bgwsvc
    vip: ************
    port: 8088
    containerName: cce-ai-service
    servicePortName: ai-service
    protocol: TCP
    creator: <EMAIL>
# CCE Alert
CCEAlert:
  Enabled: false
  Image:
    AlertCollectorImageID: cce-alert-collector-IMAGEID
    AlertManagerWebhookImageID: cce-alertmanager-webhook-IMAGEID
# CCE Instance Polling
CCEInstanceGCManager:
  Enabled: false
  Image:
    CCEInstanceGCManagerImageID: cce-instance-gc-manager-IMAGEID
  EnableBidding: true
  EnableBiddenCordonInstance: true
  EnableBiddenDeleteInstance: true
  EnableRollbackCreatedFailedInstance: false
  EnableRedundantMachineInspector: true
  RedundantMachineInspectorClusterIDs: ALL_WITH_INSTANCE_GROUP
  EnableLeaderElection: true
  LeaderElectionLock: instance-gc-manager-leader-election
  LeaderElectionLockNamespace: cce-system
  LeaderElectionIdentity: instance-gc-manager
  LeaderElectionLeaseDuration: 15
  LeaderElectionRenewDeadline: 10
  LeaderElectionRetryPeriod: 2
# 暂时不需要
#CCEServiceGroupConsole:
#  Port: 8895
#  Image:
#    ImageID: registry.baidubce.com/cce-edge/service-group-console:*******
#  Config:
#    IamUrl: http://iam.bj.bce-internal.baidu.com
#    IamDomainName: Default
#    IamDomainID: default
#    STSServiceUrl: http://sts.bj.iam.sdns.baidu.com:8586
#    CceServiceUrl: http://cce.bj.baidubce.com/api/cce/service

# CCE image plugin
CCEImagePlugin:
  Image:
    ImageID: registry.baidubce.com/cce-plugin-pro/cce-image-plugin:v2.0.1
# CCE db sync controller
CCEDBSyncController:
  Enabled: true
  Image:
    ImageID: cce-db-sync-controller-IMAGEID
  EnableTaskSync: true
  TaskSyncConcurrency: 40
  EnableInstanceGroupSync: true
  InstanceGroupSyncConcurrency: 20
  EnableWorkflowSync: false
  WorkflowSyncConcurrency: 20
# Cloud Edge Cluster Image
CloudEdgeCluster:
  CalicoNodeIptablesSetImageTag: *******
  CloudTunnelImageTag: *******
  EdgeTunnelImageTag: *******
  ServiceGroupControllerImageTag: *******
  BECLBControllerImageTag: *******
  TopologyServerImageTag: *******
CCEWorkflowController:
  Image:
    ImageID: cce-workflow-controller-IMAGEID
  MaxWorkflowCount: 200
  MaxRetentionDays: 7
  GCIntervalSeconds: 3600
PromtailEnable: false
Promtail:
  imageID: registry.baidubce.com/cce-plugin-dev/promtail:2.5.0
  lokiEndpoint: http://************/loki/api/v1/push
LokiPusherEnable: false
LokiPusher:
  replicas: 3
  redisAddress: test
  redisPassword: Abccba123CCE
  imageID: loki-pusher-IMAGEID
CCEGrafana:
  Enable: false
  GrafanaProvisioningImage: cce-grafana-provisioning-IMAGEID
CCEAIService:
  Image:
    ImageID: cce-ai-service-IMAGEID
  Port: 8687
CCEControlPlaneTargets:
  Enable: false
  Image: registry.baidubce.com/cce-plugin-pro/cce-control-plane-targets:202206291344
  ETCDPort: 3379
CCEAlertRulesApply:
  Enable: false
  Image: cce-alert-rules-apply-IMAGEID
ConsoleGrafana:
  Address: ""
  User: admin
  Password: admin@cprom@system
EnglishConsoleGrafana:
  Address: ""
  User: admin
  Password: admin@cprom@system
KubeStateMetrics:
  Enable: false
  Replicas: 3
ETCDBackup:
  Enable: false
  ETCDEndpoints: 127.0.0.1:2379
PluginConfig:
  CCELBControllerImageTag: 1.30.8
  CCECloudNodeControllerImageTag: 2025.06.17
  BLBNameMaxLength: 200
  DisableDelete: true
  EnableIaaSTag: true
CCEResourceGroupKafka:
  TLS:
    CaPath: resource-manager/certs/ca.pem
    CrtPath: resource-manager/certs/client.pem
    KeyPath: resource-manager/certs/client.key
    CA: |
      -----BEGIN CERTIFICATE-----
      MIIECjCCAfKgAwIBAgIEAdlkeTANBgkqhkiG9w0BAQsFADAbMRkwFwYDVQQDExBy
      b290LmthZmthLmJhaWR1MCAXDTE2MDkxNDEyMTI0N1oYDzIxMTYwODIxMTIxMjQ3
      WjAZMRcwFQYDVQQDEw5jYS5rYWZrYS5iYWlkdTCCASIwDQYJKoZIhvcNAQEBBQAD
      ggEPADCCAQoCggEBANI3tV3IZzkWTYwK+XLuQP6O313NsSlrMzl7gKHwIjaQ8evp
      RchEpWWAaIHBYjiLs8/yy0GsDClZpwN2XuN7U+Mpy8C5GNCzNBuoAOwvx8obwWvQ
      uWYHvVBWMr8fJCitxceO/bhWTlITAG9YZknuHW82rRugzT/R+TVQfFepxn4K7/0Z
      cHwVQWbcsw8FeHWo6cqcgDaUW5DCVlHUt/Lb1z/b4Pvz+K77J6Z6myuuqFDdWO0k
      ZAwE4blQwk8xuxLbHV5XwmDHYMZaTbtF8EpE+v5SwdA1rhTa6/Y9+N9FCpMJ+W5A
      IB4wL7EK5NMWa4F29QE+BLqG7b0WRtCmh0tUsZcCAwEAAaNWMFQwHwYDVR0jBBgw
      FoAUllewJceUj3/uP02PvpPJ4JdN1r0wEgYDVR0TAQH/BAgwBgEB/wIBADAdBgNV
      HQ4EFgQUIk7cOedQySXhnQgx0IQm23o2Ub8wDQYJKoZIhvcNAQELBQADggIBACwk
      FTZPIcZJXtUmPsNmTfyu9drCp4RiFQ4BVslWBvei6Cd5u5A2aI9VYP26ga3vBudG
      mW0swbO8PuiKhz2q9vNM7UjlLEy+sAsUy30V/AObN/lhAfhr+rAvydBbaTRRdPR9
      KW/wPh1t11xNYWzYU5cCiD3wAwEwv3/0tu1LT7FoynMIpd+3KdVlhfX/7Q21xNt9
      8/+lUkJcUcrZyGXjcEWTzuyThQIgcoK/pXf2BkNMmc5MNVo8u//Ug9cFYmT/8uzE
      KY3oRuqPxs9i1KHgW9hYmyGCpzxPcVODRCVFJX1WxMiWh8dcZLiUMvarQF8j/JcB
      yOagD7VGJe3s/CGKrHnuOakvjyKZ7e/rP3kCU2+0TqgK/owplDaAxpBsOPOyKfSm
      /rt5grn+6x9zaCKxhGvNe/11n+T1jlushc/R6lpNx3xyX4stW2PKEz4Z+uYjH+J5
      HY9AdTshCSmOeCX7kJUTtpiW71BwdIr3uB46Kjh+ojSOJzQ7E2mM2Xt6D92Or1sG
      rnPjRKOS3mwj6WQRcr9TG2UU399V6tmSo1oaWOXWhJ6bgAhFYF4pvO0DWvT623cC
      VoNOxI833pk3qviDLDc95j+J32p2F7aY6lZ2okDZp/0HndM5DLeU+m0kcjda5a6o
      +7YVwzgOQGRJKUHQkFh0w+sMN6t26T8SwqyAjH85
      -----END CERTIFICATE-----
      -----BEGIN CERTIFICATE-----
      MIIE9TCCAt2gAwIBAgIEN5J5MjANBgkqhkiG9w0BAQsFADAbMRkwFwYDVQQDExBy
      b290LmthZmthLmJhaWR1MCAXDTE2MDkxNDEyMTI0NVoYDzIxMTYwODIxMTIxMjQ1
      WjAbMRkwFwYDVQQDExByb290LmthZmthLmJhaWR1MIICIjANBgkqhkiG9w0BAQEF
      AAOCAg8AMIICCgKCAgEAnAWzbQz3epWA9wJQGHuJmKWLp6NzOFqukfS36CQjfVQu
      Np7OXyN+2arXtSabCuJFCGnPrezw1Xzcpw9MblyxTm/AP8ZfcqRERzgLzdHn4oQz
      VRBFO07wXwB91LOXi+IzNHYHATgk5tokhuXr/y+fkX3RUOwE4w6lCDuScUi5DegH
      N6qLHn4U3dywq2yZvUe6MH7vN9/Y2KDZMEgHexnAnE+b5DYFrAReIXl/0eZ88lH0
      WgEDv6Y1fooO2KfVllrc1UVD64pB6NssucL5e2yNpA/NroajtoLgIdsElkKtgPWE
      EenKuk66qntjq/R4/sCAcjeB8BhnAmwgjIP7Fn/JOrNe1Pfn/rvE660e4CDhR9Nr
      N8OYRPptt1HVoFY5JwgHAaxoGrypxGW43Np5JMYeveHn/F5Ay1xAmptVUVsGaK0P
      zPJdbC8RIKSK5c42IEk592lmj8aCmI1u0EQdhoCMmE28FXXb5dwbSq4h0AiumLL5
      5konttDrB28dDp5UFHy7OUuIgrZehO69H2O64LhKchYOaZl6hzukaK7m75O3zpSI
      WztkTus18BbnuxRFKcix1Sl//LJTLepN6OhdXkOJfC8sb6J7Pl0SJGIbqxP29yEP
      YlPTeXVfbFycgDL78NB/LOH01OBsT13quer9cHQy+Ns6Yfq2li/1MVoCvrT/9GUC
      AwEAAaM/MD0wDwYDVR0TAQH/BAUwAwEB/zALBgNVHQ8EBAMCAgQwHQYDVR0OBBYE
      FJZXsCXHlI9/7j9Nj76TyeCXTda9MA0GCSqGSIb3DQEBCwUAA4ICAQBzdbAuEJfe
      /kB6oc4Xm8RLQj9jm2aH4JmB+Tmj3nFDZdqfERrdqsEzVpPMvTt4wlO1CXyko2Tp
      l9nifoaZ/YX5VLlJq1gQtyMyXkpIGQzvQsQam//1lSGTPW6SbzDoSd0l4gFJmHSV
      6CPMADQ4gFICADGlF/xBl3r4OlWAShFHfSSL0ZeI3yrABUulbwKH2FFSjs3sY00l
      +JVd83A2pjwlLJMotbkfkoZViUHOoV8+K0QX6bDt0Rp8nS7z8pQXRvvfacxEgtnE
      0HESYMIOZR0hBF8tlRaTHEG3LsAUVuBWSM15SmS+ajUQtAML4uP48h77GmGJv2i4
      wIpGvGopxhwqB1M3SNzWqqYmtyfgoB8dZu9A+WZoD4zYgKe0ezMvii4OvF9DQco0
      9meCKGPtbeBxshJaWUv0SYOosj+1o2jWP5t6ges1/t6rf+eSrfhkOaDUbkGyF1Qc
      gEggZJDEXngxjiOXKzXejps/ih6LrwOVm8I+X92wNjxV6NytNQ3yVnho5/dNGMV4
      7TMz3yjhG9RYWomgGr4W9TvMdCYSQ3dFNL53VUygUEPqBer62k57MXT1SUy5/lr9
      DN0pRkG3w7nIv0wfvDzEAko0wftN9P9troGWKijBf90NfASDY3BPnxJ24Rx9Uqk0
      Ux8Z/plinh8yDgYkLCtn7Aqu+hpvbzi3eg==
      -----END CERTIFICATE-----
    Crt: |
      -----BEGIN CERTIFICATE-----
      MIIC/jCCAeagAwIBAgIRANWe2kuwQkVcoWc50jMxMeIwDQYJKoZIhvcNAQELBQAw
      GTEXMBUGA1UEAxMOY2Eua2Fma2EuYmFpZHUwHhcNMjAxMTI2MDUyNTQ2WhcNMzAx
      MTI0MDUyNTQ2WjBvMRAwDgYDVQQGEwdEZWZhdWx0MRAwDgYDVQQIEwdEZWZhdWx0
      MRAwDgYDVQQHEwdEZWZhdWx0MRAwDgYDVQQKEwdEZWZhdWx0MRAwDgYDVQQLEwdE
      ZWZhdWx0MRMwEQYDVQQDEwpkaW5ncGVuZzAzMIGfMA0GCSqGSIb3DQEBAQUAA4GN
      ADCBiQKBgQCMsEBQcoqlrvFYZ2Gf9TfKT5pZ0/9z/KPkxGHR9qqSgm9PTtbNKMnK
      68bQ+wrdmWvvUnJS3abJz9RJ7lt38RP+ws1Twtvm0Cn/506isN+cPxRxQeQcserl
      q6rP7dNlNTgZjQYWHyaBp4xgWPNsMlbqMt4TlhTfFBNY6HliOFDr9QIDAQABo28w
      bTAfBgNVHSMEGDAWgBQiTtw551DJJeGdCDHQhCbbejZRvzAJBgNVHRMEAjAAMBMG
      A1UdJQQMMAoGCCsGAQUFBwMCMAsGA1UdDwQEAwIF4DAdBgNVHQ4EFgQUNyz9j0yd
      JHhTLpikjFwuMnL9BL8wDQYJKoZIhvcNAQELBQADggEBAHQHscrK5rkF+/InqdAT
      QofHKnRVnEv4T5E4g5gYIC4IWhdoeqhAxsR6EH/5gfp8LmzMNOAptzH5lgSoXmmH
      IIzWjbYwrCnTyBvIcRHYquiJvbccaAGwrpnYw9yN2ibpMlVt3OVdDzeR7RLOBbcE
      eCtFDENqEcGXqWQntTjE2FQpEG13DVkG4PV6aRR12yKHEgk1atvd+W92EzmcwqTO
      YDSNPTQ0VHGb5oMTIk/ddeuIbnAxFKXW6v2tGZJkMC7T+4rGu7EF2KJPNaOmRKuq
      uI3cv187IY1MOshVp1W7rSPb918TCqmMblz4mpYoC0M5fNVYEnlB+8HIsj5qnWVC
      DBA=
      -----END CERTIFICATE-----
    Key: |
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  Endpoint: kafka.bj.baidubce.com:9091
  Topic: c42867cb52124ef4bfe51d315eea6f68__res_manager_sandbox

# 套餐校验服务
CCEArtifactService:
  Image:
    ImageID: cce-artifact-service-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    HttpPort: 8795
    # 如流群 id
    ToID:
      - 10066881
    WebHookURL: http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dd23cd6cfebf8c1d40963fa6809c6db5b
    CronEnable: false
    MySQLEndpoint: cce_service_w:XjJPujGjSDb@tcp(10.11.74.19:5979)/bdbl_cce_center_db_sandbox?charset=utf8&parseTime=true
    Users:
      - zhuangruiqing
      - v_yuanwentao
