# 该文件为渲染后结果展示，没有实际使用
# 请同步修改 /abcstack/charm/layer-cce-stack/templates/kolla/service/chart/values-private.yaml

Env: cnhzpro
Region: cnhzpro
ReleaseVersion: RELEASE_VERSION # 发布版本号，cce_stack_control.sh 会进行变量替换

PluginImageRegistry: hub.agilecloud.com:8011

IsUnionPayEnv: false # 是否是银商环境
IsPrivateEnv: false # 是否私有化环境
ValidateSubuserPermission: true # 是否开启子用户鉴权

NodeSelector: false #部署是否选择指定

BBCOpenAPI: true # 完全上线之后去除该配置
BCCDeleteOpenAPI: true

ManagedMetaClusterID:
ManagedProEnable: false

EnableDeployByAddOn: true
EnableTag: false

ReplaceMasterBLBFloatingIPByBAEndpoint: true
CheckLccEnvironment: false

InstanceDeployType: ssh

# 是否部署
CCEImageSecretEnabled: false # 私有化环境不需要
CCEPluginConfigEnabled: true
CCEClusterCRDEnabled: true
CCEClusterServiceEnabled: true
CCEClusterControllerEnabled: true
CCEMonitorServiceEnabled: true
CCEAPPServiceEnabled: true
CCEK8SEventCollectorEnabled: true
CCEK8SReportCollectorEnabled: true
CCEInstanceGroupControllerEnabled: true
CCEOIDCProviderEnabled: true
CCEInstanceNodeController: true
CCEServiceGroupConsoleEnabled: true
CCETaskController: true
CCEAIServiceEnabled: true
CCEMasterLBControllerEnabled: false
# IAM容灾
CCEIAMDisasterToleranceEnabled: false
# Giano Token
GianoToken: 505a001d68141bbfa4192f4f54ebdcde
# 健康检查
CCEClusterHealthCheckEnabled: true
CCEInstanceHealthCheckEnabled: true

# 运维检查
CCEIaaSCheckEnabled: false
CCEClusterSyncEnabled: false
CCEDevopsMySQLEnabled: false
CCEStatusAlertEnabled: false

# 日志采集
CCEMetaClusterLogCollectorEnabled: false

# 配置管理
CCEConfigRCLocalEnabled: false
CCEConfigETCCrontabEnabled: false

# Palo 采集任务
CCEPaloCollectorTestResultEnabled: false
CCEPaloCollectorStatisticsEnabled: false

# Workflow
CCEWorkflowControllerEnabled: true

# 监控采集
NodeExporterEnabled: true

# 监控服务
CCEThanosEnabled: false
CCEThanosPrometheusEnabled: false # 设置为 false, 避免升级修改 STS 副本数
CCEMasterServiceDiscoveryEnabled: false

# 镜像拉取
CCEImagePluginEnabled: false

# 私有化环境无 KubeProxy, 统一走宿主机网络
HostNetworkDNSPolicy: Default

# EIP 购买类型
EIPPurchaseType: BGP
# 默认EIP计费类型 用于部署 cce-ingress-controller 与 cce-lb-controller 部署时设置启动参数
EIPBillingMethod:

# 是否关闭新建集群的 CCM 并使用容器化组件作为替代
DisableCCM: true

# CCE 服务号
CCEServiceAccount:
  ServiceName: cce
  ServiceRoleName: BceServiceRole_SERVICE_CCE
  ServicePassword: DokkcjVbq1baUkNg80OhaFE3Q1i6hAa1
  AccessKeyID: cb407c2e1cbb4634862c4c4590936c46
  SecretAccessKey: ec7db77e0cc64dc6b6f73c83de025694
  ServiceIAMPermissionCheckName: bce:cce

# CCE 资源账号
CCEResourceAccount:
  AccountID: AccountID
  AccessKeyID: AccessKeyID
  SecretAccessKey: SecretAccessKey

# HPAS 服务账号
HPASServiceAccount:
  PaasApplication: HPAS
  ResourceAccountID: 2bd44531802c479296e49d84affb5d49

# MySQL Endpoint
MySQLEndpoint:
  CCEServiceWrite: cce_service_w:mhxzkhl@2018@tcp(xdb.cnhzpro.agilecloud.com:6203)/cce_service?charset=utf8&parseTime=true
  CaaSManagerWrite: cce_service_w:mhxzkhl@2018@tcp(xdb.cnhzpro.agilecloud.com:6203)/cce_service?charset=utf8&parseTime=true
  MonitorServiceWrite: cce_service_w:mhxzkhl@2018@tcp(xdb.cnhzpro.agilecloud.com:6203)/cce_service?charset=utf8&parseTime=true

# BCE SDK Endpoint
BCESDKEndpoint:
  AuthEndpoint: bce-api-auth.cnhzpro.agilecloud.com:8400
  UserSettingEndpoint: userconfig.agilecloud.com:8690/v1
  QuotaCenterEndpoint: userconfig.agilecloud.com:8690
  STSEndpoint: sts.agilecloud.com:8586/v1
  IAMEndpoint: iam.agilecloud.com:35357
  # 检查结尾是否需要 v1
  VPCEndpoint: bcc.cnhzpro.agilecloud.com:8680
  VPCInternalEndpoint: vpclogic.internal.cnhzpro.agilecloud.com:8680
  ImageEndpoint: bcc.cnhzpro.agilecloud.com:8680
  ImageLogicEndpoint: bcclogic.internal.cnhzpro.agilecloud.com:8680
  ZoneEndpoint: zone.internal.cnhzpro.agilecloud.com:8680
  BCCEndpoint: bcc.cnhzpro.agilecloud.com:8680
  BBCEndpoint: bbc.cnhzpro.agilecloud.com:8680
  BCCLogicEndpoint: bcclogic.internal.cnhzpro.agilecloud.com:8680
  BCCLogicalEndpoint: bccproxy.cnhzpro.agilecloud.com:20001/v1
  BCCNeutronEndpoint: bccproxy.cnhzpro.agilecloud.com:19696
  BLBLogicEndpoint: blblogic.cnhzpro.agilecloud.com:8790
  BLBInternalEndpoint: blb.cnhzpro.agilecloud.com:8885
  BLBOpenAPIEndpoint: blb.cnhzpro.agilecloud.com:8680
  AppBLBInternalAPIEndpoint: blb.cnhzpro.agilecloud.com:8885/open-api/v1
  AppBLBOpenAPIEndpoint: blb.cnhzpro.agilecloud.com:8680/v1
  EIPInternalEndpoint: blb.cnhzpro.agilecloud.com:8885
  EIPOpenAPIEndpoint: eip.cnhzpro.agilecloud.com:8680
  CCEServiceInternalEndpoint: cce-service.cnhzpro.agilecloud.com:8694
  CCEServicePluginEndpoint: cce-service.cnhzpro.agilecloud.com:8694
  CCEV2Endpoint: cce-cluster-service.cnhzpro.agilecloud.com:9793/api/cce/service/v2
  CCEMonitorEndpoint: cce-cluster-service.cnhzpro.agilecloud.com:9081
  KMSEndpoint: https://kms-service.cnhzpro.agilecloud.com
  CCEGatewayEndpoint: cce-gateway-k8s.cnhzpro.agilecloud.com:9300
  OpenCCEGatewayEndpoint: cce-gateway-k8s.cnhzpro.agilecloud.com:9300
  BCILogicEndpoint: bci.bj.baidubce.com # 暂不需要
  ETCDManagerEndpoint: cce-gateway.hz.agilecloud.com:8997
  CCEOIDCProviderEndpoint: **************:8848 # 暂不需要
  BECEndpoint: bec.baidubce.com # 暂不需要
  IAMForBECEndpoint: iam.su.bce-internal.baidu.com/v3 # 暂不需要
  FinanceEndpoint: finance.cnhzpro.agilecloud.com:8662 # 不需要，代码兼容，不为空的任意 endpoint 就行
  CPromOpenEndpoint: http://*************:18794/api/v1/cprom
  CPromEndpoint: http://*************:18794/api/v1/cprom # 同上
  CPromV2Endpoint: *************:18794
  QualifyEndpoint: qualify.bce-internal.baidu.com # 不需要，代码兼容，不为空的任意 endpoint 就行
  TagEndpoint: taglogic.internal.cnhzpro.agilecloud.com:8680/v1
  LogicTagEndpoint: bjhw-sys-rpm8059.bjhw:8777
  BCMEndpoint: bcm.gz.baidubce.com # 暂不需要
  OrderEndpoint: order.bce-internal.baidu.com # 暂不需要
  APPServiceEndpoint: fill-app-service-endpoint/api/v1
  CRMEndpoint: crm-server.baidu-int.com/v1 # 暂不需要
  CCREndpoint: 10.31.88.150:8997 # 暂不需要
  NeutronEndpoint: 10.190.89.32:9696
  BOSEndpoint: gz.bcebos.com
  BLSInternalEndpoint: bls.gz.baidubce.com:8085
  BLSServerEndpoint: https://bls.gz.baidubce.com:8185
  BLSEndpoint: bls-log.gz.baidubce.com
  ECCREndpoint: ccr.gz.baidubce.com
  BusEndpoint: register.internal-qasandbox.baidu-int.com:8985
  CCEV2EndpointForTag: cce-cluster-service.cnhzpro.agilecloud.com:9793
  LccEndpoint:
  ResourceManagerEndpoint: res-manager.bce-console.baidu-int.com
  HPASInternalEndpoint: logic-hpas.internal-qasandbox.baidu-int.com
  HPASEndpoint: hpas.bj.qasandbox.baidu-int.com
  GianoEndpoint: http://giano-cloud.baidu-int.com:9090

# CCE Cluster Service
CCEClusterService:
  Port: 9793
  Image:
    # ImageID: cce-cluster-service-IMAGEID # cce_stack_control.sh 会进行变量替换
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-cluster-service:cce-********
  Config:
    Handler: default
    RegionNetworkSegment: **********/16 # 待定
    DefaultMasterConfig:
      InstanceType: N4
      FlavorSmallCPU: 2
      FlavorSmallMEM: 4
      FlavorMediumCPU: 4
      FlavorMediumMEM: 8
      FlavorLargeCPU: 16
      FlavorLargeMEM: 32
      RootDiskType: hp1
      RootDiskSize: 100
      ImageType: System
      ImageName: 7.4 x86_64 (64bit)
      OSType: linux
      OSName: CentOS
      OSArch: x86_64 (64bit)
      OSVersion: 7.4
    ServerlessAvailable: true
    DefaultServerlessMasterConfig: {}
    SkipUpdateInstanceGroupModel: false
    EnableEdgeHubInCloudEdgeCluster: true
    EnableUpgradeWorkflow: true
    AINativeAccessKeyID: ALTAKuNsep2QOEP1LmEFM8ObQP
    AINativeSecretAccessKey: 352712a971df46e981a1d02ec78942cc
    AINativeGlobalBOSEndpoint: "bj.bcebos.com"
    AINativeGlobalBucket: cce-ai-native-bj
    AINativeBOSBucket: cce-ai-native-gztest
    AIDemoClusterID: cce-zc1xn31g
    AIDemoClusterUser: eca97e148cb74e9683d7b7240829d1ff

# CCE 插件依赖 Helm Chart
CCEPluginHelmChart:
  Image:
    # ImageID: cce-plugin-helm-chart-IMAGEID
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-plugin-helm-chart:cce-********

# CCE Cluster Controller
CCEClusterController:
  Image:
    #ImageID: cce-cluster-controller-IMAGEID # cce_stack_control.sh 会进行变量替换
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-cluster-controller:cce-********
  Config:
    CheckK8SNodeNameDuplicatedInDB: true
    ClusterConcurrency: 10
    ClusterRequeueAfterSeconds: 30
    InstanceConcurrency: 400
    InstanceRequeueAfterSeconds: 30
    TaskConcurrency: 40
    InstanceGroupMaxConcurrency: 5
    UseTaskToCreateMachines: true
    UseTaskToRemoveInstances: true
    SkipInstanceGroupModel: true
    TaskCleanerDetectionPeriod: 1800
    TaskCleanerProtectionDuration: 604800
    InstanceHandlerAnnotation: default
    ClusterHandlerAnnotation: default
    InstanceGroupHandlerAnnotation: default
    TaskHandlerAnnotation: default
    ManagedProSCName: enhanced-ssd-pl1
    BLBLayer4ClusterID:
    KubeStateMetricFTPAddress: http://bcebos.agilecloud.com:8080/baidu-container-bd/packages/kube-state-metrics/kube-state-metrics
    TelegrafFTPAddress: http://bcebos.agilecloud.com:8080/baidu-container-bd/packages/telegraf/telegraf
    LogbeatFTPAddress: http://bcebos.agilecloud.com:8080/baidu-container-bd/packages/logbeat/logbeat
    BLSAgentToken: "todo"
    CCMPath: http://bcebos.agilecloud.com:8080/baidu-container-bd/packages/cloudprovider/cce-cloud-controller-manager
    AuditerPath: http://bcebos.agilecloud.com:8080/baidu-container-bd/packages/auditer/kube-external-auditer
    KMSPath: http://bcebos.agilecloud.com:8080/baidu-container-bd/packages/kms-plugin/v1.0.0/k8s-cloudkms-plugin
    ExtenderSchedulerPath: http://bcebos.agilecloud.com:8080/baidu-container-bd/packages/gpu-extender/nvidia-share-extender-scheduler
    KubeBinPath_1_11_1: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.11.1.tar.gz
    KubeBinPath_1_11_5: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.11.5.tar.gz
    KubeBinPath_1_13_4: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.13.4.tar.gz
    KubeBinPath_1_13_10: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.13.10.tar.gz
    KubeBinPath_1_14_9: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.14.9.tar.gz
    KubeBinPath_1_16_3: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.16.3.tar.gz
    KubeBinPath_1_16_8: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.16.8.tar.gz
    KubeBinPath_1_17_17: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.17.17.tar.gz
    KubeBinPath_1_18_9: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.18.9.tar.gz
    KubeBinPath_1_20_8: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.20.8-20240601-patch-nokmem.tar.gz
    KubeBinPath_1_20_8_containerd:
    KubeBinPath_1_22_5_containerd:
    KubeBinPath_1_20_8_arm64: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.20.8-arm64.tar.gz
    KubeBinPath_1_21_14: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.21.14.tar.gz
    KubeBinPath_1_22_5: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.22.5-20231121-patch.tar.gz
    KubeBinPath_1_24_4: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.24.4.tar.gz
    KubeBinPath_1_26_9: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.26.9.tar.gz
    ContainerdBinPath_1_5_4: http://bcebos.agilecloud.com:8080/runtime/containerd/cri-containerd-cni-1.5.4-linux-amd64.tar.gz
    ContainerdBinPath_1_6_8: http://bcebos.agilecloud.com:8080/runtime/containerd/cri-containerd-cni-1.6.8-linux-amd64.tar.gz
    ContainerdBinPath_1_6_20: http://bcebos.agilecloud.com:8080/runtime/containerd/cri-containerd-cni-1.6.20-linux-amd64.tar.gz
    ContainerdBinPath_1_6_24: http://bcebos.agilecloud.com:8080/runtime/containerd/cri-containerd-cni-1.6.24-linux-amd64.tar.gz
    ContainerdBinPath_1_6_28: http://bcebos.agilecloud.com:8080/runtime/containerd/cri-containerd-cni-1.6.28-linux-amd64.tar.gz
    ContainerdBinPath_1_6_28_arm64: http://bcebos.agilecloud.com:8080/runtime/containerd/cri-containerd-cni-1.6.28-linux-arm64.tar.gz
    ContainerdBinPath_1_6_28_Rootfs: http://bcebos.agilecloud.com:8080/runtime/containerd/cri-containerd-cni-1.6.28-linux-amd64-rootfs.tar.gz
    ContainerdBinPath_1_6_36: http://bcebos.agilecloud.com:8080/runtime/containerd/cri-containerd-cni-1.6.36-linux-amd64.tar.gz
    ContainerdBinPath_1_6_36_Rootfs: http://bcebos.agilecloud.com:8080/runtime/containerd/cri-containerd-cni-1.6.36-linux-amd64-rootfs.tar.gz
    ContainerdBinPath_1_7_13: http://bcebos.agilecloud.com:8080/runtime/containerd/cri-containerd-cni-1.7.13-linux-amd64.tar.gz
    ContainerdBinPath_1_7_25: http://bcebos.agilecloud.com:8080/runtime/containerd/cri-containerd-cni-1.7.25-linux-amd64.tar.gz
    DockerBinPath_20_10_5: http://bcebos.agilecloud.com:8080/runtime/docker/docker-20.10.5.tgz
    DockerBinPath_20_10_24: http://bcebos.agilecloud.com:8080/runtime/docker/docker-20.10.24.tgz
    DockerBinPath_20_10_24_arm64: http://bcebos.agilecloud.com:8080/runtime/docker/docker-20.10.24-arm64.tgz
    NvidiaContainerToolkitPath: http://bcebos.agilecloud.com:8080/packages/nvidia-packages/nvidia-container-toolkit-6-24.tar.gz
    InsecureRegistry: hub.agilecloud.com:8011
    PauseImage_2_0: hub.agilecloud.com:8011/public/pause:2.0
    PauseImage_3_1: hub.agilecloud.com:8011/cce-public/pause:3.1
    PauseImage_3_2: registry.baidubce.com/cce-public/pause:3.2
    PauseImage_3_1_arm64: registry.baidubce.com/cce-public/pause-arm64:3.1
    KubeAPIServer_1_18_9: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.18.9
    KubeControllerManager_1_18_9: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.18.9
    KubeScheduler_1_18_9: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.18.9
    KubeAPIServer_1_16_8: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.16.8
    KubeControllerManager_1_16_8: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.16.8
    KubeScheduler_1_16_8: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.16.8
    KubeAPIServer_1_17_17: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.17.17
    KubeControllerManager_1_17_17: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.17.17
    KubeScheduler_1_17_17: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.17.17
    KubeAPIServer_1_20_8: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.20.8
    KubeControllerManager_1_20_8: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.20.8-20231120-patch
    KubeScheduler_1_20_8: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.20.8
    KubeAPIServer_1_20_8_arm64: registry.baidubce.com/cce-public/kube-apiserver-arm64:v1.20.8
    KubeControllerManager_1_20_8_arm64: registry.baidubce.com/cce-public/kube-controller-manager-arm64:v1.20.8
    KubeScheduler_1_20_8_arm64: registry.baidubce.com/cce-public/kube-scheduler-arm64:v1.20.8
    KubeAPIServer_1_21_14: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.21.14
    KubeControllerManager_1_21_14: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.21.14
    KubeScheduler_1_21_14: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.21.14
    KubeAPIServer_1_22_5: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.22.5
    KubeControllerManager_1_22_5: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.22.5-20231121-patch
    KubeScheduler_1_22_5: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.22.5
    KubeAPIServer_1_24_4: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.24.4
    KubeControllerManager_1_24_4: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.24.4
    KubeScheduler_1_24_4: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.24.4
    KubeAPIServer_1_26_9: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.26.9
    KubeControllerManager_1_26_9: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.26.9
    KubeScheduler_1_26_9: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.26.9
    KubeAPIServer_1_18_9_BilibiliMixprotocols: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.18.9-bilibili-mixprotocols
    KubeScheduler_1_18_9_BilibiliMixprotocols: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.18.9-bilibili-mixprotocols
    KubeControllerManager_1_18_9_BilibiliMixprotocols: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.18.9-bilibili-mixprotocols
    KubeExternalAuditorImage: registry.baidubce.com/cce-plugin-pro/kube-external-auditor:v1.1.2
    ManagedProKubeAPIServer_1_16_8: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.16.8
    ManagedProKubeAPIServer_1_17_17: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.17.17
    ManagedProKubeAPIServer_1_18_9: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.18.9
    ManagedProKubeAPIServer_1_20_8: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.20.8
    ManagedProKubeAPIServer_1_21_14: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.21.14
    ManagedProKubeAPIServer_1_22_5: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.22.5
    ManagedProKubeAPIServer_1_24_4: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.24.4
    ManagedProKubeAPIServer_1_26_9: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.26.9
    ManagedProKubeControllerManager_1_16_8: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.16.8
    ManagedProKubeControllerManager_1_17_17: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.17.17
    ManagedProKubeControllerManager_1_18_9: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.18.9
    ManagedProKubeControllerManager_1_20_8: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.20.8-20231120-patch
    ManagedProKubeControllerManager_1_21_14: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.21.14
    ManagedProKubeControllerManager_1_22_5: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.22.5-20231121-patch
    ManagedProKubeControllerManager_1_24_4: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.24.4
    ManagedProKubeControllerManager_1_26_9: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.26.9
    ManagedProKubeScheduler_1_16_8: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.16.8
    ManagedProKubeScheduler_1_17_17: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.17.17
    ManagedProKubeScheduler_1_18_9: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.18.9
    ManagedProKubeScheduler_1_20_8: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.20.8
    ManagedProKubeScheduler_1_21_14: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.21.14
    ManagedProKubeScheduler_1_22_5: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.22.5
    ManagedProKubeScheduler_1_24_4: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.24.4
    ManagedProKubeScheduler_1_26_9: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.26.9
    ManagedProETCDImage: registry.baidubce.com/cce-managed-pro/etcd:3.5.9
    ManagedProKubeInitImage: registry.baidubce.com/cce-managed-pro/cce-kube-master-init:v1.0.1
    ManagedProDebugerImage: registry.baidubce.com/cce-managed-pro/cce-debuger:v1.1.0
    EdgeHub: registry.baidubce.com/cce-edge/ote_edgehub:*******
    CCEAgentImage: registry.baidubce.com/cce-plugin-pro/cce-agent:20250410
    EIPPurchaseType: BGP
    KubeBinPath:
      1.28.8: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.28.8.tar.gz
      1.30.1: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.30.1.tar.gz
      1.31.1: http://bcebos.agilecloud.com:8080/baidu-container-bd/kube/kubebins-1.31.1.tar.gz
    KubeAPIServer:
      1.28.8: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.28.8
      1.30.1: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.30.1
      1.31.1: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.31.1
    KubeControllerManager:
      1.28.8: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.28.8
      1.30.1: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.30.1
      1.31.1: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.31.1
    KubeScheduler:
      1.28.8: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.28.8
      1.30.1: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.30.1
      1.31.1: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.31.1
    ManagedProKubeAPIServer:
      1.28.8: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.28.8
      1.30.1: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.30.1
      1.31.1: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.31.1
    ManagedProKubeControllerManager:
      1.28.8: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.28.8
      1.30.1: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.30.1
      1.31.1: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.31.1
    ManagedProKubeScheduler:
      1.28.8: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.28.8
      1.30.1: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.30.1
      1.31.1: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.31.1
# CCE Monitor Service
CCEMonitorService:
  Port: 9081
  Image:
    # ImageID: cce-monitor-service-IMAGEID # cce_stack_control.sh 会进行变量替换
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-monitor-service:cce-********
  Config:
    AuthEndpoint: http://bce-api-auth.cnhzpro.agilecloud.com:8400/
    AuditHost: gwgp-fv29ewwddnp.i.bdcloudapi.com
    AuditEndpoint: hestia.agilecloud.com:8483
    AuditConfigmapEndpoint: hestia.agilecloud.com:8483
    BOSEndpoint: bcebos.agilecloud.com:8080
    BOSS3Endpoint: http://s3.bcebos.agilecloud.com:8080
    BOSBucket: cce-backup-online-hz
    JPaaSAccessKeyID: cb407c2e1cbb4634862c4c4590936c46
    JPaaSSecretAccessKey: ec7db77e0cc64dc6b6f73c83de025694
    ElasticEndpoint: http://*************:8200/ # 暂不需要
    ElasticUserName: superuser # 暂不需要
    ElasticPassword: Baidu_2020 # 暂不需要
    EventCollectorTags: node01,node02 # 暂不需要
    ThanosQueryEndpoint: http://**************:8990 # 暂不需要
    CCEAlertWebHookURL: http://cce-monitor-webhook.cnhzpro.agilecloud.com:9147
  Endpoints:
    - region: "bj"
      endpoint: "http://monitor.cce-bj.baidu-int.com"
      iamEndpoint: "iam.bj.bce-internal.baidu.com"
      stsEndpoint: "sts.bj.iam.sdns.baidu.com:8586/v1"
    - region: "gz"
      endpoint: "http://monitor.cce-gz.baidu-int.com"
      iamEndpoint: "iam.gz.bce-internal.baidu.com"
      stsEndpoint: "sts.gz.iam.sdns.baidu.com:8586/v1"
    - region: "hkg"
      endpoint: "http://10.70.8.24:8081"
      iamEndpoint: "iam.hkg.bce.baidu-int.com"
      stsEndpoint: "sts.hkg.bce.baidu-int.com:8586/v1"
    - region: "bd"
      endpoint: "http://monitor.cce-bd.baidu-int.com"
      iamEndpoint: "iam.bdbl.bce.baidu-int.com"
      stsEndpoint: "sts.bdbl.bce.baidu-int.com:8586/v1"
    - region: "su"
      endpoint: "http://monitor.cce-sz.baidu-int.com"
      iamEndpoint: "iam.su.bce-internal.baidu.com"
      stsEndpoint: "sts.su.iam.sdns.baidu.com:8586/v1"
    - region: "fwh"
      endpoint: "http://10.70.16.76:8081"
      iamEndpoint: "iam.fwh.bce.baidu-int.com"
      stsEndpoint: "sts.fwh.bce.baidu-int.com:8586/v1"
    - region: "nj"
      endpoint: "http://10.70.40.30:8081"
      iamEndpoint: "iam.nj.bce.baidu-int.com"
      stsEndpoint: "sts.nj.bce.baidu-int.com/v1"
    - region: "yq"
      endpoint: "http://10.6.104.15:8081"
      iamEndpoint: "iam.yq.bce-internal.sdns.baidu.com"
      stsEndpoint: "sts.yq.iam.sdns.baidu.com:8586/v1"
    - region: "cd"
      endpoint: "http://monitor.cce-cd.baidu-int.com"
      iamEndpoint: "iam.cd.bce-internal.sdns.baidu.com"
      stsEndpoint: "sts.cd.iam.sdns.baidu.com:8586/v1"
    - region: "edge"
      endpoint: "http://monitor.cce-egsz.baidu-int.com"
      iamEndpoint: "iam.su.bce-internal.baidu.com"
      stsEndpoint: "sts.su.iam.sdns.baidu.com:8586/v1"

# CCE APP Service
CCEAPPService:
  Port: 9010
  Image:
    # ImageID: cce-app-service-IMAGEID # cce_stack_control.sh 会进行变量替换
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-app-service:cce-********
  Config:
    AuthEndpoint: http://bce-api-auth.cnhzpro.agilecloud.com:8400/
    RedisEndponit: redis://:16fd20ca849826ad00210324ffaca9a3@172.16.64.13:9250/0 # 用于 webshell
    IAMEndpoint: iam.agilecloud.com:35357
    CCEIngressControllerImageID: hub.agilecloud.com:8011/cce-plugin-pro/cce-ingress-controller:2023.11.20.1040

# CCE Cluster Sync
CCEClusterSync:
  Image:
    # ImageID: cce-cluster-sync-IMAGEID # cce_stack_control.sh 会进行变量替换
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-cluster-sync:cce-********
  Config:
    CronPeriod: 600

# CCE K8S Event collector
CCEK8SEventCollector:
  Image:
    # ImageID: k8s-event-collector-IMAGEID # cce_stack_control.sh 会进行变量替换
    ImageID: registry.agilecloud.com:5000/abc-stack/k8s-event-collector:cce-********
  Config:
    ElasticSearchEndpoint: http://*************:8200?sniff=false&cluster_id=%s&index=%s&esUserName=superuser&esUserSecret=Baidu_2020&esVersion=6
    K8SEventTag: node01 # 该k8s-event-collector实例需要收集的集群标识
    SinkType: elasticsearch # 采集输出端
    CyclePeriod: 120 # 定期轮询集群是否开启事件采集的时间间隔， 单位是秒， 默认 2*60   = 120 s
    IntervalTime: 168 # 定期清理过期es index 时间间隔，      单位是小时，默认 7 * 24 = 168 小时
    Replicas: 2 # 部署的采集器的副本数

# CCE MetaCluster log collector
CCEMetaClusterLogCollector:
  Index: drill
  Image:
    ImageID: hub.agilecloud.com:8011/jpaas-public/fluent-bit:1.5-debug
  Config:
    ElasticSearchHost: **************
    ElasticSearchPort: 9200
    ElasticSearchUser: elastic
    ElasticSearchPassword: changeme

# CCE K8S report collector
CCEK8SReportCollector:
  Image:
    # ImageID: k8s-report-collector-IMAGEID # cce_stack_control.sh 会进行变量替换
    ImageID: registry.agilecloud.com:5000/abc-stack/k8s-report-collector:cce-********

# CCE Cluster Status Alert
CCEStatusAlert:
  Image:
    # ImageID: cce-status-alert-IMAGEID # cce_stack_control.sh 会进行变量替换
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-status-alert:cce-********

# CCE Helm Service
CCEHelmService:
  Enabled: true
  Port: 9086
  Image:
    # ImageID: cce-helm-service-IMAGEID # cce_stack_control.sh 会进行变量替换
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-helm-service:cce-********
  Config:
    PublicRepoEndpoint: http://cce-chartmuseum.cnhzpro.agilecloud.com:8099/admin # public chartmuseum
    PrivateRepoPrefix: http://cce-chartmuseum.cnhzpro.agilecloud.com:8099 # private chartmuseum
    ChartPackageMaxUploadSizeInMB: 1 # 限制用户上传chart package最大1M
    ChartsUploadQuota: 0 # 限制私有仓库charts数目, 0代表不限制
    KubectlBin: kubectl-1_11
    HelmV2Bin: helm-2.12.3
    HelmV3Bin: helm-3.2.4
    TillerImage: hub.agilecloud.com:8011/jpaas-public/tiller:v2.12.3
    SocatInstallerImage: hub.agilecloud.com:8011/jpaas-public/socat-installer:alpine-3.5

# CCE Gateway
CCEGateway:
  Enabled: true
  Port: 9300
  Image:
    # ImageID: cce-gateway-IMAGEID # cce_stack_control.sh 会进行变量替换
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-gateway:cce-********
  Config:
    DBDebugLog: false
    DBConnMaxLifetimeInMin: 8
    SkipTokenValidate: false
    ClusterReconcileIntervalInSec: 600
    LogMaxBackups: 128
    LogMaxSizeInMB: 64
    LogMaxAgeInDay: 30

# CCE OIDC Provider
CCEOIDCProvider:
  Port: 9848
  Image:
    # ImageID: cce-oidc-provider-IMAGEID
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-oidc-provider:cce-********
    ImageDegist:
  TLS:
    CA:
    Crt:
    Key:
  Config:
    Issuer: https://cce.cnhzpro.agilecloud.com/oidc

# 暂不需呀
# CCE ETCD Manager
CCEETCDManager:
  Enabled: false
  Port: 9381
  Image:
    # ImageID: cce-etcd-manager-IMAGEID
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-etcd-manager:cce-********
  Config:
    Endpoints:
      - ************:8379
      - ***********:8379
      - ************:8379
    UserEndpoints:
      - ************:8379
    ETCDPrefix: /bj/serverless
    CA: |
      -----BEGIN CERTIFICATE-----
      MIICFTCCAbqgAwIBAgIUCyD0lrvMu0qG7eWLEKwNVOkiqlwwCgYIKoZIzj0EAwIw
      ZzELMAkGA1UEBhMCQ04xEDAOBgNVBAgTB0JlaUppbmcxEDAOBgNVBAcTB0JlaUpp
      bmcxDjAMBgNVBAoTBWpwYWFzMRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTEOMAwGA1UE
      AxMFanBhYXMwIBcNMjAxMTA2MTYwMzAwWhgPMjEyMDEwMTMxNjAzMDBaMGcxCzAJ
      BgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlKaW5nMRAwDgYDVQQHEwdCZWlKaW5nMQ4w
      DAYDVQQKEwVqcGFhczEUMBIGA1UECxMLY2xvdWRuYXRpdmUxDjAMBgNVBAMTBWpw
      YWFzMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaeZe1gqXdxQ12gT8ktLfPgar
      PCxlfSBfSRa66brTSS3BD0UBdEBoG3J4NVrY8FMTgNuMrT9oChbkPPA25O+zM6NC
      MEAwDgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFLoG
      snacI3wU6COdM63mLy0ZTnLMMAoGCCqGSM49BAMCA0kAMEYCIQCiiaH1xD+Wy4MV
      UUREacwMSCHnxpXWf32ZmBwgkT6b9QIhAO98budtF/4pDSSIVzR2x32yOM019OQz
      uHdKjp0kLa6j
      -----END CERTIFICATE-----

    CAKey: |
****************************************************************************************************************************************************************************************************************************************************************

    CAConfig: |
      {
        "signing": {
          "default": {
            "expiry": "876000h"
          },
          "profiles": {
            "jpaas": {
              "usages": [
                  "signing",
                  "key encipherment",
                  "server auth",
                  "client auth"
              ],
              "expiry": "876000h"
            }
          }
        }
      }

    Root: |
      -----BEGIN CERTIFICATE-----
      MIIChzCCAiygAwIBAgIUJFmN0KMTonQqyxQGnYh4lSI/1HYwCgYIKoZIzj0EAwIw
      ZzELMAkGA1UEBhMCQ04xEDAOBgNVBAgTB0JlaUppbmcxEDAOBgNVBAcTB0JlaUpp
      bmcxDjAMBgNVBAoTBWpwYWFzMRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTEOMAwGA1UE
      AxMFanBhYXMwIBcNMjAxMTA2MTYwMzAwWhgPMjEyMDEwMTMxNjAzMDBaMGUxCzAJ
      BgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlKaW5nMRAwDgYDVQQHEwdCZWlKaW5nMQ0w
      CwYDVQQKEwRyb290MRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTENMAsGA1UEAxMEcm9v
      dDBZMBMGByqGSM49AgEGCCqGSM49AwEHA0IABP7VaZiPqRStSXshaudwiMReFjW0
      ssaKsLBzvsI3cSxmwx82DQn17j2zV+nBDL8WN2wwXBtasndww51C1THAqIujgbUw
      gbIwDgYDVR0PAQH/BAQDAgWgMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcD
      AjAMBgNVHRMBAf8EAjAAMB0GA1UdDgQWBBQrHD3UsnfeiZRxlms0ZJ/RYHdeozAf
      BgNVHSMEGDAWgBS6BrJ2nCN8FOgjnTOt5i8tGU5yzDAzBgNVHREELDAqhwR/AAAB
      hwQKP3othwQKP4YghwQKP04RhwQKP1kwhwQKP2MwhwRkQP0CMAoGCCqGSM49BAMC
      A0kAMEYCIQDmQRiEhs8dThmIoELkDLbhSA0qU42I3aP11pIjKrbxxwIhAJBxBFQE
      Tnqu7eqLgCW57Qwth28lf+oq2dvBIP88Sly8
      -----END CERTIFICATE-----

    RootKey: |
****************************************************************************************************************************************************************************************************************************************************************

CCEPaloCollector:
  Image:
    # ImageID: cce-palo-collector-IMAGEID
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-palo-collector:cce-********

# CCE Config Manager
CCEConfigManager:
  Image:
    # ImageID: cce-config-manager-IMAGEID # cce_stack_control.sh 会进行变量替换
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-config-manager:cce-********

# 暂不需要
# CCE Virtual Kubelet for bci pod
CCEVirtualKubelet:
  Enabled: false
  Image: hub.agilecloud.com:8011/cce-plugin-pro/bci-virtual-kubelet:2020.10.22.1
  Port: 10350 # 指定10250以外的未使用端口，10250会和机器上kubelet冲突
  MetricsPort: 10355 # 指定10255以外的未使用端口，10255会和机器上kubelet冲突
  LogMaxBackups: 20 # 20*256MB
  NodeName: "cce-virtual-kubelet"
  ChargeApplication: "CCE" # 转移计费到资源账号
  ChargeAccessKey: "e08e132e64d246f596ad5be55cbf37d8"
  ChargeSecretKey: "6047e7d886ee49849e3d82df9259f127"

# CCE webhook
CCEValidateWebhook:
  Enabled: false
  Image:
    # ImageID: cce-validate-webhook-IMAGEID
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-validate-webhook:cce-********

# CCE IaaS Check
CCEIaaSCheck:
  Image:
    # ImageID: cce-iaas-check-IMAGEID # cce_stack_control.sh 会进行变量替换
    ImageID: registry.agilecloud.com:5000/abc-stack/cce-iaas-check:cce-********

# CCE Cluster Health Check
CCEClusterHealthCheck:
  Image:
    ImageID: cce-health-check-IMAGEID # cce_stack_control.sh 会进行变量替换

# CCE Instance Health Check
CCEInstanceHealthCheck:
  Image:
    ImageID: cce-health-check-IMAGEID # cce_stack_control.sh 会进行变量替换

# CCE Thanos
CCEThanos:
  Image:
    GrafanaProvisioningImageID: cce-grafana-provisioning-IMAGEID
  CCEServiceAccountBOSKey: e08e132e64d246f596ad5be55cbf37d8
  CCEServiceAccountBOSSecret: 6047e7d886ee49849e3d82df9259f127
  QueryAddress: http://**************:8481/select/0/prometheus

# CCE Master Service Discovery
CCEMasterServiceDiscovery:
  Image:
    ImageID: cce-master-service-discovery-IMAGEID # cce_stack_control.sh 会进行变量替换

# bgw vip sync
bgwService:
  enabled: false

# CCE Alert
CCEAlert:
  Enabled: false
  Image:
    AlertCollectorImageID: cce-alert-collector-IMAGEID
    AlertManagerWebhookImageID: cce-alertmanager-webhook-IMAGEID

# CCE Instance Polling
CCEInstanceGCManager:
  Enabled: false
  Image:
    CCEInstanceGCManagerImageID: cce-instance-gc-manager-IMAGEID
  EnableBidding: true
  EnableBiddenCordonInstance: true
  EnableBiddenDeleteInstance: true
  EnableRollbackCreatedFailedInstance: false
  EnableRedundantMachineInspector: true
  RedundantMachineInspectorClusterIDs: ALL_WITH_INSTANCE_GROUP
  EnableLeaderElection: true
  LeaderElectionLock: instance-gc-manager-leader-election
  LeaderElectionLockNamespace: cce-system
  LeaderElectionIdentity: instance-gc-manager
  LeaderElectionLeaseDuration: 15
  LeaderElectionRenewDeadline: 10
  LeaderElectionRetryPeriod: 2

# CCE Service Group Console
CCEServiceGroupConsole:
  Port: 8896
  Image:
    ImageID: registry.baidubce.com/cce-edge/service-group-console:*******
  Config:
    IamUrl: http://iam.agilecloud.com:35357
    IamDomainName: Default
    IamDomainID: default
    STSServiceUrl: http://sts.agilecloud.com:8586
    CceServiceUrl: http://cce-cluster-service.cnhzpro.agilecloud.com:9793/api/cce/service

# CCE image plugin
CCEImagePlugin:
  Image:
    ImageID: registry.baidubce.com/cce-plugin-pro/cce-image-plugin:v2.0.1

# CCE db sync controller
CCEDBSyncController:
  Enabled: true
  Image:
    ImageID: cce-db-sync-controller-IMAGEID
  EnableTaskSync: true
  TaskSyncConcurrency: 40
  EnableInstanceGroupSync: true
  InstanceGroupSyncConcurrency: 20
  EnableWorkflowSync: false
  WorkflowSyncConcurrency: 20

# Cloud Edge Cluster Image
CloudEdgeCluster:
  CalicoNodeIptablesSetImageTag: *******
  CloudTunnelImageTag: *******
  EdgeTunnelImageTag: *******
  ServiceGroupControllerImageTag: *******
  BECLBControllerImageTag: *******
  TopologyServerImageTag: *******

CCEWorkflowController:
  Image:
    ImageID: cce-workflow-controller-IMAGEID
  MaxWorkflowCount: 200
  MaxRetentionDays: 7
  GCIntervalSeconds: 3600

PromtailEnable: false
Promtail:
  imageID: registry.baidubce.com/cce-plugin-dev/promtail:2.5.0
  lokiEndpoint: http://************/loki/api/v1/push

LokiPusherEnable: false
LokiPusher:
  replicas: 3
  redisAddress: test
  redisPassword: Abccba123CCE
  imageID: loki-pusher-IMAGEID

CCEGrafana:
  Enable: false
  GrafanaProvisioningImage: cce-grafana-provisioning-IMAGEID

CCEAIService:
  Image:
    ImageID: cce-ai-service-IMAGEID
  Port: 8687

CCEControlPlaneTargets:
  Enable: false
  Image: registry.baidubce.com/cce-plugin-pro/cce-control-plane-targets:202206291344
  ETCDPort: 3379

CCEAlertRulesApply:
  Enable: false
  Image: cce-alert-rules-apply-IMAGEID

ConsoleGrafana:
  Address: ""
  User: admin
  Password: admin@cprom@system
EnglishConsoleGrafana:
  Address: ""
  User: admin
  Password: admin@cprom@system

KubeStateMetrics:
  Enable: false
  Replicas: 3

ETCDBackup:
  Enable: false
  ETCDEndpoints: 127.0.0.1:2379

PluginConfig:
  CCELBControllerImageTag: 1.30.8
  CCECloudNodeControllerImageTag: 2025.06.17
  BLBNameMaxLength: 200
  DisableDelete: true
  EnableIaaSTag: true
CCEResourceGroupKafka:
  TLS:
    CaPath: resource-manager/certs/ca.pem
    CrtPath: resource-manager/certs/client.pem
    KeyPath: resource-manager/certs/client.key
    CA: |
      -----BEGIN CERTIFICATE-----
      MIIECjCCAfKgAwIBAgIEAdlkeTANBgkqhkiG9w0BAQsFADAbMRkwFwYDVQQDExBy
      b290LmthZmthLmJhaWR1MCAXDTE2MDkxNDEyMTI0N1oYDzIxMTYwODIxMTIxMjQ3
      WjAZMRcwFQYDVQQDEw5jYS5rYWZrYS5iYWlkdTCCASIwDQYJKoZIhvcNAQEBBQAD
      ggEPADCCAQoCggEBANI3tV3IZzkWTYwK+XLuQP6O313NsSlrMzl7gKHwIjaQ8evp
      RchEpWWAaIHBYjiLs8/yy0GsDClZpwN2XuN7U+Mpy8C5GNCzNBuoAOwvx8obwWvQ
      uWYHvVBWMr8fJCitxceO/bhWTlITAG9YZknuHW82rRugzT/R+TVQfFepxn4K7/0Z
      cHwVQWbcsw8FeHWo6cqcgDaUW5DCVlHUt/Lb1z/b4Pvz+K77J6Z6myuuqFDdWO0k
      ZAwE4blQwk8xuxLbHV5XwmDHYMZaTbtF8EpE+v5SwdA1rhTa6/Y9+N9FCpMJ+W5A
      IB4wL7EK5NMWa4F29QE+BLqG7b0WRtCmh0tUsZcCAwEAAaNWMFQwHwYDVR0jBBgw
      FoAUllewJceUj3/uP02PvpPJ4JdN1r0wEgYDVR0TAQH/BAgwBgEB/wIBADAdBgNV
      HQ4EFgQUIk7cOedQySXhnQgx0IQm23o2Ub8wDQYJKoZIhvcNAQELBQADggIBACwk
      FTZPIcZJXtUmPsNmTfyu9drCp4RiFQ4BVslWBvei6Cd5u5A2aI9VYP26ga3vBudG
      mW0swbO8PuiKhz2q9vNM7UjlLEy+sAsUy30V/AObN/lhAfhr+rAvydBbaTRRdPR9
      KW/wPh1t11xNYWzYU5cCiD3wAwEwv3/0tu1LT7FoynMIpd+3KdVlhfX/7Q21xNt9
      8/+lUkJcUcrZyGXjcEWTzuyThQIgcoK/pXf2BkNMmc5MNVo8u//Ug9cFYmT/8uzE
      KY3oRuqPxs9i1KHgW9hYmyGCpzxPcVODRCVFJX1WxMiWh8dcZLiUMvarQF8j/JcB
      yOagD7VGJe3s/CGKrHnuOakvjyKZ7e/rP3kCU2+0TqgK/owplDaAxpBsOPOyKfSm
      /rt5grn+6x9zaCKxhGvNe/11n+T1jlushc/R6lpNx3xyX4stW2PKEz4Z+uYjH+J5
      HY9AdTshCSmOeCX7kJUTtpiW71BwdIr3uB46Kjh+ojSOJzQ7E2mM2Xt6D92Or1sG
      rnPjRKOS3mwj6WQRcr9TG2UU399V6tmSo1oaWOXWhJ6bgAhFYF4pvO0DWvT623cC
      VoNOxI833pk3qviDLDc95j+J32p2F7aY6lZ2okDZp/0HndM5DLeU+m0kcjda5a6o
      +7YVwzgOQGRJKUHQkFh0w+sMN6t26T8SwqyAjH85
      -----END CERTIFICATE-----
      -----BEGIN CERTIFICATE-----
      MIIE9TCCAt2gAwIBAgIEN5J5MjANBgkqhkiG9w0BAQsFADAbMRkwFwYDVQQDExBy
      b290LmthZmthLmJhaWR1MCAXDTE2MDkxNDEyMTI0NVoYDzIxMTYwODIxMTIxMjQ1
      WjAbMRkwFwYDVQQDExByb290LmthZmthLmJhaWR1MIICIjANBgkqhkiG9w0BAQEF
      AAOCAg8AMIICCgKCAgEAnAWzbQz3epWA9wJQGHuJmKWLp6NzOFqukfS36CQjfVQu
      Np7OXyN+2arXtSabCuJFCGnPrezw1Xzcpw9MblyxTm/AP8ZfcqRERzgLzdHn4oQz
      VRBFO07wXwB91LOXi+IzNHYHATgk5tokhuXr/y+fkX3RUOwE4w6lCDuScUi5DegH
      N6qLHn4U3dywq2yZvUe6MH7vN9/Y2KDZMEgHexnAnE+b5DYFrAReIXl/0eZ88lH0
      WgEDv6Y1fooO2KfVllrc1UVD64pB6NssucL5e2yNpA/NroajtoLgIdsElkKtgPWE
      EenKuk66qntjq/R4/sCAcjeB8BhnAmwgjIP7Fn/JOrNe1Pfn/rvE660e4CDhR9Nr
      N8OYRPptt1HVoFY5JwgHAaxoGrypxGW43Np5JMYeveHn/F5Ay1xAmptVUVsGaK0P
      zPJdbC8RIKSK5c42IEk592lmj8aCmI1u0EQdhoCMmE28FXXb5dwbSq4h0AiumLL5
      5konttDrB28dDp5UFHy7OUuIgrZehO69H2O64LhKchYOaZl6hzukaK7m75O3zpSI
      WztkTus18BbnuxRFKcix1Sl//LJTLepN6OhdXkOJfC8sb6J7Pl0SJGIbqxP29yEP
      YlPTeXVfbFycgDL78NB/LOH01OBsT13quer9cHQy+Ns6Yfq2li/1MVoCvrT/9GUC
      AwEAAaM/MD0wDwYDVR0TAQH/BAUwAwEB/zALBgNVHQ8EBAMCAgQwHQYDVR0OBBYE
      FJZXsCXHlI9/7j9Nj76TyeCXTda9MA0GCSqGSIb3DQEBCwUAA4ICAQBzdbAuEJfe
      /kB6oc4Xm8RLQj9jm2aH4JmB+Tmj3nFDZdqfERrdqsEzVpPMvTt4wlO1CXyko2Tp
      l9nifoaZ/YX5VLlJq1gQtyMyXkpIGQzvQsQam//1lSGTPW6SbzDoSd0l4gFJmHSV
      6CPMADQ4gFICADGlF/xBl3r4OlWAShFHfSSL0ZeI3yrABUulbwKH2FFSjs3sY00l
      +JVd83A2pjwlLJMotbkfkoZViUHOoV8+K0QX6bDt0Rp8nS7z8pQXRvvfacxEgtnE
      0HESYMIOZR0hBF8tlRaTHEG3LsAUVuBWSM15SmS+ajUQtAML4uP48h77GmGJv2i4
      wIpGvGopxhwqB1M3SNzWqqYmtyfgoB8dZu9A+WZoD4zYgKe0ezMvii4OvF9DQco0
      9meCKGPtbeBxshJaWUv0SYOosj+1o2jWP5t6ges1/t6rf+eSrfhkOaDUbkGyF1Qc
      gEggZJDEXngxjiOXKzXejps/ih6LrwOVm8I+X92wNjxV6NytNQ3yVnho5/dNGMV4
      7TMz3yjhG9RYWomgGr4W9TvMdCYSQ3dFNL53VUygUEPqBer62k57MXT1SUy5/lr9
      DN0pRkG3w7nIv0wfvDzEAko0wftN9P9troGWKijBf90NfASDY3BPnxJ24Rx9Uqk0
      Ux8Z/plinh8yDgYkLCtn7Aqu+hpvbzi3eg==
      -----END CERTIFICATE-----
    Crt: |
      -----BEGIN CERTIFICATE-----
      MIIC/TCCAeWgAwIBAgIQJ6amKyzRQymAslu2IkxFvjANBgkqhkiG9w0BAQsFADAZ
      MRcwFQYDVQQDEw5jYS5rYWZrYS5iYWlkdTAeFw0yMDEyMDgxMTI5MjRaFw0zMDEy
      MDYxMTI5MjRaMG8xEDAOBgNVBAYTB0RlZmF1bHQxEDAOBgNVBAgTB0RlZmF1bHQx
      EDAOBgNVBAcTB0RlZmF1bHQxEDAOBgNVBAoTB0RlZmF1bHQxEDAOBgNVBAsTB0Rl
      ZmF1bHQxEzARBgNVBAMTCmRpbmdwZW5nMDMwgZ8wDQYJKoZIhvcNAQEBBQADgY0A
      MIGJAoGBAItW9nj/fH95eXlQ6tchP/kCdYzaf6jsqhYNICOd5X1yuiFpB2zDN19h
      97vSxehNairVB4la2miPPrSXA4H5jCagByNcJDoKnjfPaViIyzGklBmmY+nTg4TM
      M+en/4XOjQoJC817yXmJLcU2Rzbfj+1oXmzjG6IAgc3BvBllM+YtAgMBAAGjbzBt
      MB8GA1UdIwQYMBaAFCJO3DnnUMkl4Z0IMdCEJtt6NlG/MAkGA1UdEwQCMAAwEwYD
      VR0lBAwwCgYIKwYBBQUHAwIwCwYDVR0PBAQDAgXgMB0GA1UdDgQWBBQjhkHGpfuY
      roIzfNMqHf1Vw7ephzANBgkqhkiG9w0BAQsFAAOCAQEAV8izQLBXNJ/UOaPDLoKK
      RGzbNojyjYRgruQgCO4beFOpZzZbGo9V7KMcFMVp8/DydhwmkZA/kcg1oDMDqBnH
      3oF5IrFMDe80wfpq56v9mwFgnsVr/eTC5WLlYyvkQgHcd7wXIej5/CdCXOAKwAwI
      rPcjtUfLUph5oPFsftlnoWVEmxRbS0Cq/aLrPQP2DoH1dPKFsZgxbpe748ZWFLi+
      SDFYW/39DT0gI8CTs690MxGhHWUK9mJ1voYfl5qj8Rluz6spzlAxO02+R3RqLUpt
      lsnmxI8HcM39OVGe2XXiah74l1v4wVzOlnj+UCYZfCKl6apwA5gcM4BleJN19mpS
      AA==
      -----END CERTIFICATE-----
    Key: |
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  Endpoint: kafka.bj.baidubce.com:9091
  Topic: c42867cb52124ef4bfe51d315eea6f68__res_manager_online

# 套餐校验服务
CCEArtifactService:
  Image:
    ImageID: cce-artifact-service-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    HttpPort: 8795
    # 如流群 id
    ToID:
      - 10066881
    WebHookURL: http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dd23cd6cfebf8c1d40963fa6809c6db5b
    CronEnable: false
    MySQLEndpoint: cce_service_w:XjJPujGjSDb@tcp(10.11.74.19:5979)/bdbl_cce_center_db_sandbox?charset=utf8&parseTime=true
    Users:
      - zhuangruiqing
      - v_yuanwentao
