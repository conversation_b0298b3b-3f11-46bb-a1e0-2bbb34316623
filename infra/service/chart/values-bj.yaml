# cce-stack
# values.yaml 包含各地域不同配置

Env: bj
Region: bj
ReleaseVersion: RELEASE_VERSION # 发布版本号，cce_stack_control.sh 会进行变量替换
PluginImageRegistry: registry.baidubce.com
IsUnionPayEnv: false # 是否是银商环境
IsPrivateEnv: false # 是否私有化环境
ValidateSubuserPermission: true # 是否开启子用户鉴权
NodeSelector: false #部署是否选择指定
BBCOpenAPI: true # 完全上线之后去除该配置
BCCDeleteOpenAPI: true
ManagedMetaClusterID: cce-lmgj910q
ManagedProEnable: true
EnableDeployByAddOn: true
EnableTag: true
ReplaceMasterBLBFloatingIPByBAEndpoint: true
CheckLccEnvironment: true
InstanceDeployType: oos
# 是否部署
CCEImageSecretEnabled: true
CCEPluginConfigEnabled: true
CCEClusterCRDEnabled: true
CCEClusterServiceEnabled: true
CCEClusterControllerEnabled: true
CCEMonitorServiceEnabled: true
CCEAPPServiceEnabled: true
CCEK8SEventCollectorEnabled: true
CCEStatusAlertEnabled: true
CCEK8SReportCollectorEnabled: true
CCEClusterSyncEnabled: true
CCEInstanceGroupControllerEnabled: true
CCEMetaClusterLogCollectorEnabled: false
CCEOIDCProviderEnabled: true
CCEDevopsMySQLEnabled: true
CCEIaaSCheckEnabled: true
CCEInstanceNodeController: true
CCEServiceGroupConsoleEnabled: true
CCETaskController: true
CCEAIServiceEnabled: true
CCEArtifactServiceEnabled: true
CCEMasterLBControllerEnabled: true
# IAM容灾
CCEIAMDisasterToleranceEnabled: true
# Giano Token
GianoToken: 505a001d68141bbfa4192f4f54ebdcde
# 健康检查
CCEClusterHealthCheckEnabled: true
CCEInstanceHealthCheckEnabled: true
# 配置管理
CCEConfigRCLocalEnabled: true
CCEConfigETCCrontabEnabled: true
# Palo 采集任务
CCEPaloCollectorTestResultEnabled: true
CCEPaloCollectorStatisticsEnabled: true
# Workflow
CCEWorkflowControllerEnabled: true
# 监控采集
NodeExporterEnabled: true
# 监控服务
CCEThanosEnabled: false
CCEThanosPrometheusEnabled: false # 设置为 false, 避免升级修改 STS 副本数
CCEMasterServiceDiscoveryEnabled: true
CCEMasterServiceDiscoveryConfig:
  minClusterSize: 50
  VIPAccountIDs:
    - 0fbda4ce246045bd8c89f63b188e8e0d
    - 5fbb9a10b20d4b088bdb0e860ae80be5
    - 81a2f02ef8a746a28663ce573482c255
    - 8ba8fe2d049948d88d04918b78ba820c
    - fe4be8bc905e45008e611ffb40ee8971
    - 2eb3c07539c24770a31e8941d972c1a4
    - bd02e6f86ed1483d91ee6bc591f6bd7c
  targetGroups:
    - path: /v1/metrics
      name: bci-cn-endpoint
      namespace: bci-cn-endpoint
      targets:
        - bjlsh-bcc-online-com2-261.bjlsh:18326
        - bjrs-bcc-online-com2-183.bjrs:18326
        - bjrs-bcc-online-com2-706.bjrs:18326
        - bjrs-bcc-online-com0-029.bjrs:18326
        - bjdd-bcc-online-com2-052.bjdd:18326
        - bjdd-bcc-online-com2-566.bjdd:18326
        - bjdd-bcc-online-com2-057.bjdd:18326
        - bjdd-bcc-online-com2-098.bjdd:18326
        - bjdd-bcc-online-com2-064.bjdd:18326
        - bjdd-bcc-online-com2-096.bjdd:18326
        - bjdd-bcc-online-com2-007.bjdd:18326
        - bjdd-bcc-online-com2-2286.bjdd:18326
        - bjdd-bcc-online-com2-2287.bjdd:18326
        - bjdd-bcc-online-com2-2633.bjdd:18326
        - bjdd-bcc-online-com2-2625.bjdd:18326
        - bjdd-bcc-online-com2-2626.bjdd:18326
        - bjdd-bcc-online-com2-2627.bjdd:18326
        - bjdd-bcc-online-com2-2628.bjdd:18326
        - bjdd-bcc-online-com2-2638.bjdd:18326
        - bjdd-bcc-online-com2-2640.bjdd:18326
# 镜像拉取
CCEImagePluginEnabled: false
HostNetworkDNSPolicy: ClusterFirstWithHostNet
# EIP 购买类型
EIPPurchaseType:
# 默认EIP计费类型 用于部署 cce-ingress-controller 与 cce-lb-controller 部署时设置启动参数
EIPBillingMethod:
# 是否关闭新建集群的 CCM 并使用容器化组件作为替代
DisableCCM: true
# CCE 服务号
CCEServiceAccount:
  ServiceName: cce
  ServiceRoleName: BceServiceRole_SERVICE_CCE
  ServicePassword: c8QBqhOKxmGu0Sl2p13guMil3U2grbYt
  AccessKeyID: c0d3690042364b47a8e5b91e3d834b83
  SecretAccessKey: fe2e4b7c70234a2c926a159112996820
  ServiceIAMPermissionCheckName: bce:cce
# CCE 资源账号
CCEResourceAccount:
  AccountID: 02fa0a988caf43a9a5c0270310347581
  AccessKeyID: 2daa909dc0cb4c7892cc409135cf0c37
  SecretAccessKey: 6a4eea1ca53543be80e06601756613aa
# HPAS 服务账号
HPASServiceAccount:
  PaasApplication: HPAS
  ResourceAccountID: cceb2cdd6e6b437dafca4328e1399bb1
# MySQL Endpoint
MySQLEndpoint:
  CCECenterDBWrite: cce_service_w:XjJPujGjSDb@tcp(mysql.cce-bd.baidu-int.com:5979)/bdbl_cce_center_db?charset=utf8&parseTime=true
  CCEServiceWrite: cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true
  CaaSManagerWrite: cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true
  MonitorServiceWrite: cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true
# BCE SDK Endpoint
BCESDKEndpoint:
  AuthEndpoint: **************:8400
  UserSettingEndpoint: settings.bce-internal.baidu.com/v1
  QuotaCenterEndpoint: quota-center.baidubce.com
  STSEndpoint: sts.bj.iam.sdns.baidu.com:8586/v1
  IAMEndpoint: iam.bj.bce-internal.baidu.com
  IAMAgentEndpoint: agent-iam.bj.sdns.baidu.com
  PriceEndpoint: pricing.bce-internal.baidu.com
  # 检查结尾是否需要 v1
  VPCEndpoint: bcc.bj.baidubce.com
  VPCInternalEndpoint: vpc.bj.bce-internal.baidu.com
  ImageEndpoint: bcc.bj.baidubce.com
  ImageLogicEndpoint: bcclogic.bce-internal.baidu.com
  ZoneEndpoint: zone.bj.bce-internal.baidu.com
  BCCEndpoint: bcc.bj.baidubce.com
  ResourceManagerEndpoint: res-manager.bce-console.baidu-int.com
  BBCEndpoint: bbc.bj.baidubce.com
  BCCLogicEndpoint: bcclogic.bce-internal.baidu.com
  BCCLogicalEndpoint: bccproxy.bj.bce-internal.baidu.com:18080/v1
  BCCNeutronEndpoint: bccproxy.bj.bce-internal.baidu.com:19696
  BLBLogicEndpoint: blblogic.bj.bce-internal.baidu.com
  BLBInternalEndpoint: blb.bj.bce-internal.baidu.com
  BLBOpenAPIEndpoint: blb.bj.baidubce.com
  AppBLBInternalAPIEndpoint: blb.bj.bce-internal.baidu.com/open-api/v1
  AppBLBOpenAPIEndpoint: blb.bj.baidubce.com/v1
  EIPInternalEndpoint: blb.bj.bce-internal.baidu.com
  EIPOpenAPIEndpoint: eip.bj.baidubce.com
  CCEServiceInternalEndpoint: cce-service.bj.baidubce.com:8693
  CCEServicePluginEndpoint: cce-service.bj.baidubce.com:8693
  CCEV2Endpoint: cce.bj.baidubce.com/api/cce/service/v2
  CCEMonitorEndpoint: cce.bj.baidubce.com/api/cce/monitor
  ProbeServiceEndpoint: cce.bj.baidubce.com
  KMSEndpoint: https://bkm.bj.baidubce.com
  CCEGatewayEndpoint: cce-service.bj.baidubce.com:8300
  OpenCCEGatewayEndpoint: cce-gateway.bj.baidubce.com
  BCILogicEndpoint: bci.bj.baidubce.com
  ETCDManagerEndpoint: cce-service.bj.baidubce.com:8381
  CCEOIDCProviderEndpoint: cce-service.bj.baidubce.com:8848
  BECEndpoint: bec.baidubce.com
  IAMForBECEndpoint: iam.su.bce-internal.baidu.com/v3
  FinanceEndpoint: finance.bce-internal.baidu.com:8662
  CPromOpenEndpoint: cprom.bj.baidubce.com/v1
  CPromEndpoint: http://cce-service.bj.baidubce.com:8794/api/v1/cprom
  CPromV2Endpoint: cprom.bj.baidubce.com
  QualifyEndpoint: qualify.bce-internal.baidu.com
  TagEndpoint: tag.baidubce.com/v1
  LogicTagEndpoint: taglogic.bj.bce-internal.baidu.com
  BCMEndpoint: bcm.bj.baidubce.com
  OrderEndpoint: order.bce-internal.baidu.com
  APPServiceEndpoint: cce-service.bj.baidubce.com:8010/api/v1
  CRMEndpoint: crm-server.baidu-int.com/v1
  CCREndpoint: ccr.baidubce.com
  NeutronEndpoint: neutron.bj.bce-internal.baidu.com:9696
  BOSEndpoint: bj.bcebos.com
  BLSInternalEndpoint: bls.bj.baidubce.com:8085
  BLSServerEndpoint: https://bls.bj.baidubce.com:8185
  BLSEndpoint: bls-log.bj.baidubce.com
  ECCREndpoint: ccr.bj.baidubce.com
  ECCRP2PManagerVIP: *************
  BusEndpoint: register.bce-console.sdns.baidu.com
  CCEV2EndpointForTag: cce.bj.baidubce.com
  LccEndpoint: logic-lcc.bj.bce.baidu-int.com
  HPASInternalEndpoint: hpaslogic.bj.bce.baidu-int.com
  HPASEndpoint: hpas.bj.baidubce.com
  GianoEndpoint: http://giano-cloud.baidu-int.com:9090

# CCE Cluster Service
CCEClusterService:
  Port: 8793
  Image:
    ImageID: cce-cluster-service-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    Handler: default
    RegionNetworkSegment: *********/16
    DefaultMasterConfig:
      InstanceType: N3
      FlavorSmallCPU: 2
      FlavorSmallMEM: 4
      FlavorMediumCPU: 4
      FlavorMediumMEM: 8
      FlavorLargeCPU: 16
      FlavorLargeMEM: 32
      RootDiskType: hp1
      RootDiskSize: 100
      ImageType: System
      ImageName: 7.3 x86_64 (64bit)
      OSType: linux
      OSName: CentOS
      OSArch: x86_64 (64bit)
      OSVersion: 7.3
    ServerlessAvailable: true
    DefaultServerlessMasterConfig: {}
    SkipUpdateInstanceGroupModel: false
    EnableEdgeHubInCloudEdgeCluster: true
    EnableUpgradeWorkflow: true
    AINativeAccessKeyID: ALTAKuNsep2QOEP1LmEFM8ObQP
    AINativeSecretAccessKey: 352712a971df46e981a1d02ec78942cc
    AINativeGlobalBOSEndpoint: "bj.bcebos.com"
    AINativeGlobalBucket: cce-ai-native-bj
    AINativeBOSBucket: cce-ai-native-bj
    AIDemoClusterID: cce-137q6kly
    AIDemoClusterUser: 02fa0a988caf43a9a5c0270310347581
# CCE 插件依赖 Helm Chart
CCEPluginHelmChart:
  Image:
    ImageID: cce-plugin-helm-chart-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Cluster Controller
CCEClusterController:
  Image:
    ImageID: cce-cluster-controller-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    PartialSuccessInterval: "5m"
    CheckK8SNodeNameDuplicatedInDB: true
    ClusterConcurrency: 10
    ClusterRequeueAfterSeconds: 30
    InstanceConcurrency: 400
    InstanceRequeueAfterSeconds: 30
    TaskConcurrency: 40
    InstanceGroupMaxConcurrency: 5
    UseTaskToCreateMachines: true
    UseTaskToRemoveInstances: true
    SkipInstanceGroupModel: true
    DefaultConcurrencyCreatingInstances: 100
    TaskCleanerDetectionPeriod: 1800
    TaskCleanerProtectionDuration: 259200
    InstanceHandlerAnnotation: default
    ClusterHandlerAnnotation: default
    InstanceGroupHandlerAnnotation: default
    TaskHandlerAnnotation: default
    ManagedProSCName: enhanced-ssd-pl1
    PartialSuccessEnable: true
    BLBLayer4ClusterID:
    KubeStateMetricFTPAddress: http://bj.bcebos.com/baidu-container/packages/kube-state-metrics/kube-state-metrics
    TelegrafFTPAddress: http://bj.bcebos.com/baidu-container/packages/telegraf/telegraf
    LogbeatFTPAddress: http://bj.bcebos.com/baidu-container/packages/logbeat/logbeat
    BLSAgentToken: ba1d829d-a2d9-4955-70d2-743a31a07648
    CCMPath: http://bj.bcebos.com/baidu-container/packages/cloudprovider/cce-cloud-controller-manager
    AuditerPath: http://bj.bcebos.com/baidu-container/packages/auditer/kube-external-auditer
    KMSPath: http://bj.bcebos.com/baidu-container/packages/kms-plugin/v1.0.0/k8s-cloudkms-plugin
    ExtenderSchedulerPath: http://bj.bcebos.com/baidu-container/packages/gpu-extender/nvidia-share-extender-scheduler
    KubeBinPath_1_11_1: http://bj.bcebos.com/baidu-container/kube/kubebins-1.11.1.tar.gz
    KubeBinPath_1_11_5: http://bj.bcebos.com/baidu-container/kube/kubebins-1.11.5.tar.gz
    KubeBinPath_1_13_4: http://bj.bcebos.com/baidu-container/kube/kubebins-1.13.4.tar.gz
    KubeBinPath_1_13_10: http://bj.bcebos.com/baidu-container/kube/kubebins-1.13.10.tar.gz
    KubeBinPath_1_14_9: http://bj.bcebos.com/baidu-container/kube/kubebins-1.14.9.tar.gz
    KubeBinPath_1_16_3: http://bj.bcebos.com/baidu-container/kube/kubebins-1.16.3.tar.gz
    KubeBinPath_1_16_8: http://bj.bcebos.com/baidu-container/kube/kubebins-1.16.8.tar.gz
    KubeBinPath_1_17_17: http://bj.bcebos.com/baidu-container/kube/kubebins-1.17.17.tar.gz
    KubeBinPath_1_18_9: http://bj.bcebos.com/baidu-container/kube/kubebins-1.18.9.tar.gz
    KubeBinPath_1_18_9_BilibiliMixprotocols: http://bj.bcebos.com/baidu-container/kube/kubebins-1.18.9-bilibili-mixprotocols.tar.gz
    KubeBinPath_1_20_8: http://bj.bcebos.com/baidu-container/kube/kubebins-1.20.8-20240601-patch-nokmem.tar.gz
    KubeBinPath_1_20_8_containerd: http://bj.bcebos.com/baidu-container/kube/kubebins-1.20.8-containerd-nokmem.tar.gz
    KubeBinPath_1_21_14: http://bj.bcebos.com/baidu-container/kube/kubebins-1.21.14.tar.gz
    KubeBinPath_1_22_5_containerd: http://bj.bcebos.com/baidu-container/kube/kubebins-1.22.5-containerd.tar.gz
    KubeBinPath_1_24_4_containerd: http://bj.bcebos.com/baidu-container/kube/kubebins-1.24.4-containerd.tar.gz
    KubeBinPath_1_20_8_arm64: http://bj.bcebos.com/baidu-container/kube/kubebins-1.20.8-arm64.tar.gz
    KubeBinPath_1_22_5: http://bj.bcebos.com/baidu-container/kube/kubebins-1.22.5-20231121-patch.tar.gz
    KubeBinPath_1_24_4: http://bj.bcebos.com/baidu-container/kube/kubebins-1.24.4.tar.gz
    KubeBinPath_1_26_9: http://bj.bcebos.com/baidu-container/kube/kubebins-1.26.9.tar.gz
    ContainerdBinPath_1_5_4: http://bj.bcebos.com/baidu-container/runtime/containerd/cri-containerd-cni-1.5.4-linux-amd64.tar.gz
    ContainerdBinPath_1_6_8: http://bj.bcebos.com/baidu-container/runtime/containerd/cri-containerd-cni-1.6.8-linux-amd64.tar.gz
    ContainerdBinPath_1_6_20: http://bj.bcebos.com/baidu-container/runtime/containerd/cri-containerd-cni-1.6.20-linux-amd64.tar.gz
    ContainerdBinPath_1_6_24: http://bj.bcebos.com/baidu-container/runtime/containerd/cri-containerd-cni-1.6.24-linux-amd64.tar.gz
    ContainerdBinPath_1_6_28: http://bj.bcebos.com/baidu-container/runtime/containerd/cri-containerd-cni-1.6.28-linux-amd64.tar.gz
    ContainerdBinPath_1_6_28_arm64: http://bj.bcebos.com/baidu-container/runtime/containerd/cri-containerd-cni-1.6.28-linux-arm64.tar.gz
    ContainerdBinPath_1_6_28_Rootfs: http://bj.bcebos.com/baidu-container/runtime/containerd/cri-containerd-cni-1.6.28-linux-amd64-rootfs.tar.gz
    ContainerdBinPath_1_6_36: http://bj.bcebos.com/baidu-container/runtime/containerd/cri-containerd-cni-1.6.36-linux-amd64.tar.gz
    ContainerdBinPath_1_6_36_Rootfs: http://bj.bcebos.com/baidu-container/runtime/containerd/cri-containerd-cni-1.6.36-linux-amd64-rootfs.tar.gz
    ContainerdBinPath_1_7_13: http://bj.bcebos.com/baidu-container/runtime/containerd/cri-containerd-cni-1.7.13-linux-amd64.tar.gz
    ContainerdBinPath_1_7_25: http://bj.bcebos.com/baidu-container/runtime/containerd/cri-containerd-cni-1.7.25-linux-amd64.tar.gz
    DockerBinPath_20_10_5: http://bj.bcebos.com/baidu-container/runtime/docker/docker-20.10.5.tgz
    DockerBinPath_20_10_24: http://bj.bcebos.com/baidu-container/runtime/docker/docker-20.10.24.tgz
    DockerBinPath_20_10_24_arm64: http://bj.bcebos.com/baidu-container/runtime/docker/docker-20.10.24-arm64.tgz
    NvidiaContainerToolkitPath: http://baidu-container.bj.bcebos.com/packages/nvidia-packages/nvidia-container-toolkit-25-04-29.tar.gz
    PauseImage_2_0: registry.baidubce.com/public/pause:2.0
    PauseImage_3_1: registry.baidubce.com/cce-public/pause:3.1
    PauseImage_3_2: registry.baidubce.com/cce-public/pause:3.2
    PauseImage_3_1_arm64: registry.baidubce.com/cce-public/pause-arm64:3.1
    KubeAPIServer_1_18_9: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.18.9
    KubeControllerManager_1_18_9: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.18.9
    KubeScheduler_1_18_9: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.18.9
    KubeAPIServer_1_16_8: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.16.8
    KubeControllerManager_1_16_8: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.16.8
    KubeScheduler_1_16_8: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.16.8
    KubeAPIServer_1_17_17: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.17.17
    KubeControllerManager_1_17_17: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.17.17
    KubeScheduler_1_17_17: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.17.17
    KubeAPIServer_1_20_8: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.20.8
    KubeControllerManager_1_20_8: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.20.8-20231120-patch
    KubeScheduler_1_20_8: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.20.8
    KubeAPIServer_1_20_8_arm64: registry.baidubce.com/cce-public/kube-apiserver-arm64:v1.20.8
    KubeControllerManager_1_20_8_arm64: registry.baidubce.com/cce-public/kube-controller-manager-arm64:v1.20.8
    KubeScheduler_1_20_8_arm64: registry.baidubce.com/cce-public/kube-scheduler-arm64:v1.20.8
    KubeAPIServer_1_21_14: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.21.14
    KubeControllerManager_1_21_14: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.21.14
    KubeScheduler_1_21_14: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.21.14
    KubeAPIServer_1_22_5: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.22.5
    KubeControllerManager_1_22_5: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.22.5-20231121-patch
    KubeScheduler_1_22_5: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.22.5
    KubeAPIServer_1_24_4: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.24.4
    KubeControllerManager_1_24_4: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.24.4
    KubeScheduler_1_24_4: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.24.4
    KubeAPIServer_1_26_9: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.26.9
    KubeControllerManager_1_26_9: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.26.9
    KubeScheduler_1_26_9: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.26.9
    KubeAPIServer_1_18_9_BilibiliMixprotocols: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.18.9-bilibili-mixprotocols
    KubeScheduler_1_18_9_BilibiliMixprotocols: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.18.9-bilibili-mixprotocols
    KubeControllerManager_1_18_9_BilibiliMixprotocols: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.18.9-bilibili-mixprotocols
    KubeExternalAuditorImage: registry.baidubce.com/cce-plugin-pro/kube-external-auditor:v1.1.2
    ManagedProKubeAPIServer_1_16_8: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.16.8
    ManagedProKubeAPIServer_1_17_17: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.17.17
    ManagedProKubeAPIServer_1_18_9: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.18.9
    ManagedProKubeAPIServer_1_20_8: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.20.8
    ManagedProKubeAPIServer_1_21_14: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.21.14
    ManagedProKubeAPIServer_1_22_5: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.22.5
    ManagedProKubeAPIServer_1_24_4: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.24.4
    ManagedProKubeAPIServer_1_26_9: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.26.9
    ManagedProKubeControllerManager_1_16_8: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.16.8
    ManagedProKubeControllerManager_1_17_17: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.17.17
    ManagedProKubeControllerManager_1_18_9: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.18.9
    ManagedProKubeControllerManager_1_20_8: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.20.8-20231120-patch
    ManagedProKubeControllerManager_1_21_14: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.21.14
    ManagedProKubeControllerManager_1_22_5: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.22.5-20231121-patch
    ManagedProKubeControllerManager_1_24_4: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.24.4
    ManagedProKubeControllerManager_1_26_9: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.26.9
    ManagedProKubeScheduler_1_16_8: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.16.8
    ManagedProKubeScheduler_1_17_17: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.17.17
    ManagedProKubeScheduler_1_18_9: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.18.9
    ManagedProKubeScheduler_1_20_8: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.20.8
    ManagedProKubeScheduler_1_21_14: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.21.14
    ManagedProKubeScheduler_1_22_5: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.22.5
    ManagedProKubeScheduler_1_24_4: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.24.4
    ManagedProKubeScheduler_1_26_9: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.26.9
    ManagedProETCDImage: registry.baidubce.com/cce-managed-pro/etcd:3.5.9
    ManagedProKubeInitImage: registry.baidubce.com/cce-managed-pro/cce-kube-master-init:v1.0.1
    ManagedProDebugerImage: registry.baidubce.com/cce-managed-pro/cce-debuger:v1.1.0
    EdgeHub: registry.baidubce.com/cce-edge/ote_edgehub:*******
    CCEAgentImage: registry.baidubce.com/cce-plugin-pro/cce-agent:20250410
    EIPPurchaseType:
    KubeBinPath:
      1.28.8: http://bj.bcebos.com/baidu-container/kube/kubebins-1.28.8.tar.gz
      1.30.1: http://bj.bcebos.com/baidu-container/kube/kubebins-1.30.1.tar.gz
      1.31.1: http://bj.bcebos.com/baidu-container/kube/kubebins-1.31.1.tar.gz
    KubeAPIServer:
      1.28.8: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.28.8
      1.30.1: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.30.1
      1.31.1: registry.baidubce.com/cce-public/kube-apiserver-amd64:v1.31.1
    KubeControllerManager:
      1.28.8: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.28.8
      1.30.1: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.30.1
      1.31.1: registry.baidubce.com/cce-public/kube-controller-manager-amd64:v1.31.1
    KubeScheduler:
      1.28.8: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.28.8
      1.30.1: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.30.1
      1.31.1: registry.baidubce.com/cce-public/kube-scheduler-amd64:v1.31.1
    ManagedProKubeAPIServer:
      1.28.8: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.28.8
      1.30.1: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.30.1
      1.31.1: registry.baidubce.com/cce-managed-pro/kube-apiserver:v1.31.1
    ManagedProKubeControllerManager:
      1.28.8: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.28.8
      1.30.1: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.30.1
      1.31.1: registry.baidubce.com/cce-managed-pro/kube-controller-manager:v1.31.1
    ManagedProKubeScheduler:
      1.28.8: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.28.8
      1.30.1: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.30.1
      1.31.1: registry.baidubce.com/cce-managed-pro/kube-scheduler:v1.31.1
# CCE Monitor Service
CCEMonitorService:
  Port: 8081
  Image:
    ImageID: cce-monitor-service-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    # http://wiki.baidu.com/pages/viewpage.action?pageId=507042483
    AuditHost: gwgp-fv29ewwddnp.i.bdcloudapi.com
    AuditEndpoint: ************:8981 # monitor-service 查询 BCT 审计事件 Endpoint
    AuditConfigmapEndpoint: engine-a.apigw.sdns.baidu.com # external-audit A 区推送地址
    BOSEndpoint: bj.bcebos.com
    BOSS3Endpoint: http://s3.%s.bcebos.com
    BOSBucket: cce-backup-online-bj
    AuthEndpoint: http://**************:8400/
    JPaaSAccessKeyID: ALTAKvhzrJjp6AnLBFZ6yfn2MX
    JPaaSSecretAccessKey: 878956c054f54684ad0d7e2d96c34486
    ElasticEndpoint: http://*************:8200/
    ElasticUserName: superuser
    ElasticPassword: Baidu_2020
    EventCollectorTags: node01,node02
    ThanosQueryEndpoint: http://**************:8990 # 北京申请 VIP *********** 暂不可用
    CCEAlertWebHookURL: http://cce-monitor-webhook.baidubce.com
  Endpoints:
    - region: "bj"
      endpoint: "http://monitor.cce-bj.baidu-int.com"
      iamEndpoint: "iam.bj.bce-internal.baidu.com"
      stsEndpoint: "sts.bj.iam.sdns.baidu.com:8586/v1"
    - region: "gz"
      endpoint: "http://monitor.cce-gz.baidu-int.com"
      iamEndpoint: "iam.gz.bce-internal.baidu.com"
      stsEndpoint: "sts.gz.iam.sdns.baidu.com:8586/v1"
    - region: "hkg"
      endpoint: "http://**********:8081"
      iamEndpoint: "iam.hkg.bce.baidu-int.com"
      stsEndpoint: "sts.hkg.bce.baidu-int.com:8586/v1"
    - region: "bd"
      endpoint: "http://monitor.cce-bd.baidu-int.com"
      iamEndpoint: "iam.bdbl.bce.baidu-int.com"
      stsEndpoint: "sts.bdbl.bce.baidu-int.com:8586/v1"
    - region: "su"
      endpoint: "http://monitor.cce-sz.baidu-int.com"
      iamEndpoint: "iam.su.bce-internal.baidu.com"
      stsEndpoint: "sts.su.iam.sdns.baidu.com:8586/v1"
    - region: "fwh"
      endpoint: "http://10.70.16.76:8081"
      iamEndpoint: "iam.fwh.bce.baidu-int.com"
      stsEndpoint: "sts.fwh.bce.baidu-int.com:8586/v1"
    - region: "nj"
      endpoint: "http://10.70.40.30:8081"
      iamEndpoint: "iam.nj.bce.baidu-int.com"
      stsEndpoint: "sts.nj.bce.baidu-int.com/v1"
    - region: "yq"
      endpoint: "http://10.6.104.15:8081"
      iamEndpoint: "iam.yq.bce-internal.sdns.baidu.com"
      stsEndpoint: "sts.yq.iam.sdns.baidu.com:8586/v1"
    - region: "cd"
      endpoint: "http://monitor.cce-cd.baidu-int.com"
      iamEndpoint: "iam.cd.bce-internal.sdns.baidu.com"
      stsEndpoint: "sts.cd.iam.sdns.baidu.com:8586/v1"
    - region: "edge"
      endpoint: "http://monitor.cce-egsz.baidu-int.com"
      iamEndpoint: "iam.su.bce-internal.baidu.com"
      stsEndpoint: "sts.su.iam.sdns.baidu.com:8586/v1"
# CCE APP Service
CCEAPPService:
  Port: 8010
  Image:
    ImageID: cce-app-service-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    AuthEndpoint: http://cce-service.bj.baidubce.com:8400/
    RedisEndponit: redis://:@10.11.35.200:9000/0 # 用于 webshell
    CCEIngressControllerImageID: registry.baidubce.com/cce-plugin-pro/cce-ingress-controller:1.29.5
# CCE Cluster Sync
CCEClusterSync:
  Image:
    ImageID: cce-cluster-sync-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    CronPeriod: 600
# CCE K8S Event collector
CCEK8SEventCollector:
  Image:
    ImageID: k8s-event-collector-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    ElasticSearchEndpoint: http://*************:8200?sniff=false&cluster_id=%s&index=%s&esUserName=superuser&esUserSecret=Baidu_2020&esVersion=6
    K8SEventTag: node01 # 该k8s-event-collector实例需要收集的集群标识
    SinkType: elasticsearch # 采集输出端
    CyclePeriod: 120 # 定期轮询集群是否开启事件采集的时间间隔， 单位是秒， 默认 2*60   = 120 s
    IntervalTime: 168 # 定期清理过期es index 时间间隔，      单位是小时，默认 7 * 24 = 168 小时
    Replicas: 2 # 部署的采集器的副本数
# CCE MetaCluster log collector
CCEMetaClusterLogCollector:
  Index: bj
  Image:
    ImageID: registry.baidubce.com/cce-plugin-pro/fluent-bit:1.5.1-debug
  Config:
    ElasticSearchHost: *************
    ElasticSearchPort: 8200
    ElasticSearchUser: elastic
    ElasticSearchPassword: changeme
# CCE K8S report collector
CCEK8SReportCollector:
  Image:
    ImageID: k8s-report-collector-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Cluster Status Alert
CCEStatusAlert:
  Image:
    ImageID: cce-status-alert-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Helm Service
CCEHelmService:
  Enabled: true
  Port: 8086
  Image:
    ImageID: cce-helm-service-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    PublicRepoEndpoint: http://**************:8098 # public chartmuseum
    PrivateRepoPrefix: http://**************:8099 # private chartmuseum
    ChartPackageMaxUploadSizeInMB: 1 # 限制用户上传chart package最大1M
    ChartsUploadQuota: 0 # 限制私有仓库charts数目, 0代表不限制
    KubectlBin: kubectl-1_11
    HelmV2Bin: helm-2.12.3
    HelmV3Bin: helm-3.2.4
    TillerImage: registry.baidubce.com/jpaas-public/tiller:v2.12.3
    SocatInstallerImage: registry.baidubce.com/jpaas-public/socat-installer:alpine-3.5
# CCE Gateway
CCEGateway:
  Enabled: true
  Port: 8300
  Image:
    ImageID: cce-gateway-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    DBDebugLog: false
    DBConnMaxLifetimeInMin: 8
    SkipTokenValidate: false
    ClusterReconcileIntervalInSec: 600
    LogMaxBackups: 128
    LogMaxSizeInMB: 64
    LogMaxAgeInDay: 30
# CCE OIDC Provider
CCEOIDCProvider:
  Port: 8848
  Image:
    ImageID: cce-oidc-provider-IMAGEID
    ImageDegist:
  TLS:
    CA:
    Crt:
    Key:
  Config:
    Issuer: https://cce.bj.baidubce.com/oidc
# CCE ETCD Manager
CCEETCDManager:
  Enabled: true
  Port: 8381
  Image:
    ImageID: cce-etcd-manager-IMAGEID
  Config:
    Endpoints:
      - *************:8379
      - *************:8379
      - *************:8379
    UserEndpoints:
      - cce-etcd.bj.baidubce.com:8379
    ETCDPrefix: /bj/serverless
    CA: |
      -----BEGIN CERTIFICATE-----
      MIICFTCCAbqgAwIBAgIUCyD0lrvMu0qG7eWLEKwNVOkiqlwwCgYIKoZIzj0EAwIw
      ZzELMAkGA1UEBhMCQ04xEDAOBgNVBAgTB0JlaUppbmcxEDAOBgNVBAcTB0JlaUpp
      bmcxDjAMBgNVBAoTBWpwYWFzMRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTEOMAwGA1UE
      AxMFanBhYXMwIBcNMjAxMTA2MTYwMzAwWhgPMjEyMDEwMTMxNjAzMDBaMGcxCzAJ
      BgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlKaW5nMRAwDgYDVQQHEwdCZWlKaW5nMQ4w
      DAYDVQQKEwVqcGFhczEUMBIGA1UECxMLY2xvdWRuYXRpdmUxDjAMBgNVBAMTBWpw
      YWFzMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaeZe1gqXdxQ12gT8ktLfPgar
      PCxlfSBfSRa66brTSS3BD0UBdEBoG3J4NVrY8FMTgNuMrT9oChbkPPA25O+zM6NC
      MEAwDgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFLoG
      snacI3wU6COdM63mLy0ZTnLMMAoGCCqGSM49BAMCA0kAMEYCIQCiiaH1xD+Wy4MV
      UUREacwMSCHnxpXWf32ZmBwgkT6b9QIhAO98budtF/4pDSSIVzR2x32yOM019OQz
      uHdKjp0kLa6j
      -----END CERTIFICATE-----
    CAKey: |
****************************************************************************************************************************************************************************************************************************************************************
    CAConfig: |
      {
        "signing": {
          "default": {
            "expiry": "876000h"
          },
          "profiles": {
            "jpaas": {
              "usages": [
                  "signing",
                  "key encipherment",
                  "server auth",
                  "client auth"
              ],
              "expiry": "876000h"
            }
          }
        }
      }
    Root: |
      -----BEGIN CERTIFICATE-----
      MIICuDCCAl6gAwIBAgIUXikOFNmDcpmm9oB8x8uEwPpkoCMwCgYIKoZIzj0EAwIw
      ZzELMAkGA1UEBhMCQ04xEDAOBgNVBAgTB0JlaUppbmcxEDAOBgNVBAcTB0JlaUpp
      bmcxDjAMBgNVBAoTBWpwYWFzMRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTEOMAwGA1UE
      AxMFanBhYXMwIBcNMjEwODA0MDcxMDAwWhgPMjEyMTA3MTEwNzEwMDBaMGUxCzAJ
      BgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlKaW5nMRAwDgYDVQQHEwdCZWlKaW5nMQ0w
      CwYDVQQKEwRyb290MRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTENMAsGA1UEAxMEcm9v
      dDBZMBMGByqGSM49AgEGCCqGSM49AwEHA0IABFfpteiAORvvS9KWLA7fI7qA84Vm
      Pihidudwx7aQ2xJvFoABipJdezoqeXhdai4cgHcw6HMlOYtZ5ie/gpDhnHejgecw
      geQwDgYDVR0PAQH/BAQDAgWgMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcD
      AjAMBgNVHRMBAf8EAjAAMB0GA1UdDgQWBBT/ipOxzSp22GLsbYd2n4XPKgYH3DAf
      BgNVHSMEGDAWgBS6BrJ2nCN8FOgjnTOt5i8tGU5yzDBlBgNVHREEXjBcghhjY2Ut
      ZXRjZC5iai5iYWlkdWJjZS5jb22HBH8AAAGHBAo/ei2HBAo/hiCHBAo/ThGHBAo/
      WTCHBAo/YzCHBArjchuHBArjahCHBArjahGHBGRAUEeHBGRA/QIwCgYIKoZIzj0E
      AwIDSAAwRQIgfmN+aIZ2K69Q47c7Euis4zpV1Tubxfck6MT7vnEXCkgCIQC4B+GH
      qQSVTC6kaW9RKmExyXJHqmj8q0H63YhP4XwrUA==
      -----END CERTIFICATE-----
    RootKey: |
****************************************************************************************************************************************************************************************************************************************************************
CCEPaloCollector:
  Image:
    ImageID: cce-palo-collector-IMAGEID
# CCE Config Manager
CCEConfigManager:
  Image:
    ImageID: cce-config-manager-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Virtual Kubelet for bci pod
CCEVirtualKubelet:
  Enabled: true
  Image: registry.baidubce.com/cce-plugin-pro/bci-virtual-kubelet:2020.10.22.1
  Port: 10350 # 指定10250以外的未使用端口，10250会和机器上kubelet冲突
  MetricsPort: 10355 # 指定10255以外的未使用端口，10255会和机器上kubelet冲突
  LogMaxBackups: 20 # 20*256MB
  NodeName: "cce-virtual-kubelet"
  ChargeApplication: "CCE" # 转移计费到资源账号
  ChargeAccessKey: "e08e132e64d246f596ad5be55cbf37d8"
  ChargeSecretKey: "6047e7d886ee49849e3d82df9259f127"
# CCE webhook
CCEValidateWebhook:
  Enabled: true
  Image:
    ImageID: cce-validate-webhook-IMAGEID
# CCE IaaS Check
CCEIaaSCheck:
  Image:
    ImageID: cce-iaas-check-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Cluster Health Check
CCEClusterHealthCheck:
  Image:
    ImageID: cce-health-check-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Instance Health Check
CCEInstanceHealthCheck:
  Image:
    ImageID: cce-health-check-IMAGEID # cce_stack_control.sh 会进行变量替换
# CCE Thanos
CCEThanos:
  Image:
    GrafanaProvisioningImageID: cce-grafana-provisioning-IMAGEID
  CCEServiceAccountBOSKey: e08e132e64d246f596ad5be55cbf37d8
  CCEServiceAccountBOSSecret: 6047e7d886ee49849e3d82df9259f127
  QueryAddress: http://**************:8481/select/0/prometheus
# CCE Master Service Discovery
CCEMasterServiceDiscovery:
  Image:
    ImageID: cce-master-service-discovery-IMAGEID # cce_stack_control.sh 会进行变量替换
# bgw vip sync
bgwService:
  enabled: false
# CCE Alert
CCEAlert:
  Enabled: true
  Image:
    AlertCollectorImageID: cce-alert-collector-IMAGEID
    AlertManagerWebhookImageID: cce-alertmanager-webhook-IMAGEID
# CCE Instance Polling
CCEInstanceGCManager:
  Enabled: true
  Image:
    CCEInstanceGCManagerImageID: cce-instance-gc-manager-IMAGEID
  EnableBidding: true
  EnableBiddenCordonInstance: true
  EnableBiddenDeleteInstance: true
  EnableRollbackCreatedFailedInstance: false
  EnableRedundantMachineInspector: true
  RedundantMachineInspectorClusterIDs: ALL_WITH_INSTANCE_GROUP
  EnableLeaderElection: true
  LeaderElectionLock: instance-gc-manager-leader-election
  LeaderElectionLockNamespace: cce-system
  LeaderElectionIdentity: instance-gc-manager
  LeaderElectionLeaseDuration: 15
  LeaderElectionRenewDeadline: 10
  LeaderElectionRetryPeriod: 2
# CCE Service Group Console
CCEServiceGroupConsole:
  Port: 8896
  Image:
    ImageID: registry.baidubce.com/cce-edge/service-group-console:*******
  Config:
    IamUrl: http://iam.bj.bce-internal.baidu.com
    IamDomainName: Default
    IamDomainID: default
    STSServiceUrl: http://sts.bj.iam.sdns.baidu.com:8586
    CceServiceUrl: http://cce.bj.baidubce.com/api/cce/service
# CCE image plugin
CCEImagePlugin:
  Image:
    ImageID: registry.baidubce.com/cce-plugin-pro/cce-image-plugin:v2.0.1
# CCE db sync controller
CCEDBSyncController:
  Enabled: true
  Image:
    ImageID: cce-db-sync-controller-IMAGEID
  EnableTaskSync: true
  TaskSyncConcurrency: 40
  EnableInstanceGroupSync: true
  InstanceGroupSyncConcurrency: 20
  EnableWorkflowSync: false
  WorkflowSyncConcurrency: 20
# Cloud Edge Cluster Image
CloudEdgeCluster:
  CalicoNodeIptablesSetImageTag: *******
  CloudTunnelImageTag: *******
  EdgeTunnelImageTag: *******
  ServiceGroupControllerImageTag: *******
  BECLBControllerImageTag: *******
  TopologyServerImageTag: *******
CCEVMAgent:
  Enabled: true
  Replicas: 4
  NodeSelector: true
  NodeName: vmagent-node
  Customized: true
  CPU: 60000m
  Memory: 96Gi
  RemoteWriteURL: http://**************:8480/insert/0/prometheus/api/v1/write
  Image: registry.baidubce.com/cce-plugin-dev/vmagent:v1.70.0-cce
CCEWorkflowController:
  Image:
    ImageID: cce-workflow-controller-IMAGEID
  MaxWorkflowCount: 200
  MaxRetentionDays: 7
  GCIntervalSeconds: 3600
PromtailEnable: true
Promtail:
  imageID: registry.baidubce.com/cce-plugin-dev/promtail:2.5.0
  lokiEndpoint: http://************/loki/api/v1/push
LokiPusherEnable: true
LokiPusher:
  replicas: 6
  redisAddress: *************:6379
  redisPassword: Abccba123CCE
  imageID: loki-pusher-IMAGEID
CCEGrafana:
  Enable: false
  GrafanaProvisioningImage: cce-grafana-provisioning-IMAGEID
CCEAIService:
  Image:
    ImageID: cce-ai-service-IMAGEID
  Port: 8687
CCEControlPlaneTargets:
  Enable: true
  Image: registry.baidubce.com/cce-plugin-pro/cce-control-plane-targets:202206291344
  ETCDPort: 3379
CCEAlertRulesApply:
  Enable: false
  Image: cce-alert-rules-apply-IMAGEID
ConsoleGrafana:
  Address: "http://*************:3000"
  User: admin
  Password: admin@cprom@system
EnglishConsoleGrafana:
  Address: "http://*************:3000"
  User: admin
  Password: admin@cprom@system
KubeStateMetrics:
  Enable: true
  KSMCRDEnable: true
  Replicas: 3
ETCDBackup:
  BOSEndpoint: bj.bcebos.com
  BOSAccessKey: 33063b7994ef4af8bf44100de810e97f
  BOSSecretKey: c2f7dea8a12741b5a35c1ce6d5194279
  BOSBucket: cce-cluster-backup-bj
  Enable: true
  ETCDEndpoints: ************:3379
CCEMasterLBController:
  MetaClusterID: cce-lmgj910q
  Image:
    ImageID: cce-master-lb-controller-IMAGEID
PluginConfig:
  # 灰度环境上线cce_network_v2
  CCENetworkV1Compatible: false
  CCELBControllerImageTag: 1.30.8
  CCECloudNodeControllerImageTag: 2025.06.17
  BLBNameMaxLength: 200
  DisableDelete: true
  EnableIaaSTag: true
CCEResourceGroupKafka:
  TLS:
    CaPath: resource-manager/certs/ca.pem
    CrtPath: resource-manager/certs/client.pem
    KeyPath: resource-manager/certs/client.key
    CA: |
      -----BEGIN CERTIFICATE-----
      MIIECjCCAfKgAwIBAgIEAdlkeTANBgkqhkiG9w0BAQsFADAbMRkwFwYDVQQDExBy
      b290LmthZmthLmJhaWR1MCAXDTE2MDkxNDEyMTI0N1oYDzIxMTYwODIxMTIxMjQ3
      WjAZMRcwFQYDVQQDEw5jYS5rYWZrYS5iYWlkdTCCASIwDQYJKoZIhvcNAQEBBQAD
      ggEPADCCAQoCggEBANI3tV3IZzkWTYwK+XLuQP6O313NsSlrMzl7gKHwIjaQ8evp
      RchEpWWAaIHBYjiLs8/yy0GsDClZpwN2XuN7U+Mpy8C5GNCzNBuoAOwvx8obwWvQ
      uWYHvVBWMr8fJCitxceO/bhWTlITAG9YZknuHW82rRugzT/R+TVQfFepxn4K7/0Z
      cHwVQWbcsw8FeHWo6cqcgDaUW5DCVlHUt/Lb1z/b4Pvz+K77J6Z6myuuqFDdWO0k
      ZAwE4blQwk8xuxLbHV5XwmDHYMZaTbtF8EpE+v5SwdA1rhTa6/Y9+N9FCpMJ+W5A
      IB4wL7EK5NMWa4F29QE+BLqG7b0WRtCmh0tUsZcCAwEAAaNWMFQwHwYDVR0jBBgw
      FoAUllewJceUj3/uP02PvpPJ4JdN1r0wEgYDVR0TAQH/BAgwBgEB/wIBADAdBgNV
      HQ4EFgQUIk7cOedQySXhnQgx0IQm23o2Ub8wDQYJKoZIhvcNAQELBQADggIBACwk
      FTZPIcZJXtUmPsNmTfyu9drCp4RiFQ4BVslWBvei6Cd5u5A2aI9VYP26ga3vBudG
      mW0swbO8PuiKhz2q9vNM7UjlLEy+sAsUy30V/AObN/lhAfhr+rAvydBbaTRRdPR9
      KW/wPh1t11xNYWzYU5cCiD3wAwEwv3/0tu1LT7FoynMIpd+3KdVlhfX/7Q21xNt9
      8/+lUkJcUcrZyGXjcEWTzuyThQIgcoK/pXf2BkNMmc5MNVo8u//Ug9cFYmT/8uzE
      KY3oRuqPxs9i1KHgW9hYmyGCpzxPcVODRCVFJX1WxMiWh8dcZLiUMvarQF8j/JcB
      yOagD7VGJe3s/CGKrHnuOakvjyKZ7e/rP3kCU2+0TqgK/owplDaAxpBsOPOyKfSm
      /rt5grn+6x9zaCKxhGvNe/11n+T1jlushc/R6lpNx3xyX4stW2PKEz4Z+uYjH+J5
      HY9AdTshCSmOeCX7kJUTtpiW71BwdIr3uB46Kjh+ojSOJzQ7E2mM2Xt6D92Or1sG
      rnPjRKOS3mwj6WQRcr9TG2UU399V6tmSo1oaWOXWhJ6bgAhFYF4pvO0DWvT623cC
      VoNOxI833pk3qviDLDc95j+J32p2F7aY6lZ2okDZp/0HndM5DLeU+m0kcjda5a6o
      +7YVwzgOQGRJKUHQkFh0w+sMN6t26T8SwqyAjH85
      -----END CERTIFICATE-----
      -----BEGIN CERTIFICATE-----
      MIIE9TCCAt2gAwIBAgIEN5J5MjANBgkqhkiG9w0BAQsFADAbMRkwFwYDVQQDExBy
      b290LmthZmthLmJhaWR1MCAXDTE2MDkxNDEyMTI0NVoYDzIxMTYwODIxMTIxMjQ1
      WjAbMRkwFwYDVQQDExByb290LmthZmthLmJhaWR1MIICIjANBgkqhkiG9w0BAQEF
      AAOCAg8AMIICCgKCAgEAnAWzbQz3epWA9wJQGHuJmKWLp6NzOFqukfS36CQjfVQu
      Np7OXyN+2arXtSabCuJFCGnPrezw1Xzcpw9MblyxTm/AP8ZfcqRERzgLzdHn4oQz
      VRBFO07wXwB91LOXi+IzNHYHATgk5tokhuXr/y+fkX3RUOwE4w6lCDuScUi5DegH
      N6qLHn4U3dywq2yZvUe6MH7vN9/Y2KDZMEgHexnAnE+b5DYFrAReIXl/0eZ88lH0
      WgEDv6Y1fooO2KfVllrc1UVD64pB6NssucL5e2yNpA/NroajtoLgIdsElkKtgPWE
      EenKuk66qntjq/R4/sCAcjeB8BhnAmwgjIP7Fn/JOrNe1Pfn/rvE660e4CDhR9Nr
      N8OYRPptt1HVoFY5JwgHAaxoGrypxGW43Np5JMYeveHn/F5Ay1xAmptVUVsGaK0P
      zPJdbC8RIKSK5c42IEk592lmj8aCmI1u0EQdhoCMmE28FXXb5dwbSq4h0AiumLL5
      5konttDrB28dDp5UFHy7OUuIgrZehO69H2O64LhKchYOaZl6hzukaK7m75O3zpSI
      WztkTus18BbnuxRFKcix1Sl//LJTLepN6OhdXkOJfC8sb6J7Pl0SJGIbqxP29yEP
      YlPTeXVfbFycgDL78NB/LOH01OBsT13quer9cHQy+Ns6Yfq2li/1MVoCvrT/9GUC
      AwEAAaM/MD0wDwYDVR0TAQH/BAUwAwEB/zALBgNVHQ8EBAMCAgQwHQYDVR0OBBYE
      FJZXsCXHlI9/7j9Nj76TyeCXTda9MA0GCSqGSIb3DQEBCwUAA4ICAQBzdbAuEJfe
      /kB6oc4Xm8RLQj9jm2aH4JmB+Tmj3nFDZdqfERrdqsEzVpPMvTt4wlO1CXyko2Tp
      l9nifoaZ/YX5VLlJq1gQtyMyXkpIGQzvQsQam//1lSGTPW6SbzDoSd0l4gFJmHSV
      6CPMADQ4gFICADGlF/xBl3r4OlWAShFHfSSL0ZeI3yrABUulbwKH2FFSjs3sY00l
      +JVd83A2pjwlLJMotbkfkoZViUHOoV8+K0QX6bDt0Rp8nS7z8pQXRvvfacxEgtnE
      0HESYMIOZR0hBF8tlRaTHEG3LsAUVuBWSM15SmS+ajUQtAML4uP48h77GmGJv2i4
      wIpGvGopxhwqB1M3SNzWqqYmtyfgoB8dZu9A+WZoD4zYgKe0ezMvii4OvF9DQco0
      9meCKGPtbeBxshJaWUv0SYOosj+1o2jWP5t6ges1/t6rf+eSrfhkOaDUbkGyF1Qc
      gEggZJDEXngxjiOXKzXejps/ih6LrwOVm8I+X92wNjxV6NytNQ3yVnho5/dNGMV4
      7TMz3yjhG9RYWomgGr4W9TvMdCYSQ3dFNL53VUygUEPqBer62k57MXT1SUy5/lr9
      DN0pRkG3w7nIv0wfvDzEAko0wftN9P9troGWKijBf90NfASDY3BPnxJ24Rx9Uqk0
      Ux8Z/plinh8yDgYkLCtn7Aqu+hpvbzi3eg==
      -----END CERTIFICATE-----
    Crt: |
      -----BEGIN CERTIFICATE-----
      MIIC/TCCAeWgAwIBAgIQJ6amKyzRQymAslu2IkxFvjANBgkqhkiG9w0BAQsFADAZ
      MRcwFQYDVQQDEw5jYS5rYWZrYS5iYWlkdTAeFw0yMDEyMDgxMTI5MjRaFw0zMDEy
      MDYxMTI5MjRaMG8xEDAOBgNVBAYTB0RlZmF1bHQxEDAOBgNVBAgTB0RlZmF1bHQx
      EDAOBgNVBAcTB0RlZmF1bHQxEDAOBgNVBAoTB0RlZmF1bHQxEDAOBgNVBAsTB0Rl
      ZmF1bHQxEzARBgNVBAMTCmRpbmdwZW5nMDMwgZ8wDQYJKoZIhvcNAQEBBQADgY0A
      MIGJAoGBAItW9nj/fH95eXlQ6tchP/kCdYzaf6jsqhYNICOd5X1yuiFpB2zDN19h
      97vSxehNairVB4la2miPPrSXA4H5jCagByNcJDoKnjfPaViIyzGklBmmY+nTg4TM
      M+en/4XOjQoJC817yXmJLcU2Rzbfj+1oXmzjG6IAgc3BvBllM+YtAgMBAAGjbzBt
      MB8GA1UdIwQYMBaAFCJO3DnnUMkl4Z0IMdCEJtt6NlG/MAkGA1UdEwQCMAAwEwYD
      VR0lBAwwCgYIKwYBBQUHAwIwCwYDVR0PBAQDAgXgMB0GA1UdDgQWBBQjhkHGpfuY
      roIzfNMqHf1Vw7ephzANBgkqhkiG9w0BAQsFAAOCAQEAV8izQLBXNJ/UOaPDLoKK
      RGzbNojyjYRgruQgCO4beFOpZzZbGo9V7KMcFMVp8/DydhwmkZA/kcg1oDMDqBnH
      3oF5IrFMDe80wfpq56v9mwFgnsVr/eTC5WLlYyvkQgHcd7wXIej5/CdCXOAKwAwI
      rPcjtUfLUph5oPFsftlnoWVEmxRbS0Cq/aLrPQP2DoH1dPKFsZgxbpe748ZWFLi+
      SDFYW/39DT0gI8CTs690MxGhHWUK9mJ1voYfl5qj8Rluz6spzlAxO02+R3RqLUpt
      lsnmxI8HcM39OVGe2XXiah74l1v4wVzOlnj+UCYZfCKl6apwA5gcM4BleJN19mpS
      AA==
      -----END CERTIFICATE-----
    Key: |
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  Endpoint: kafka.bj.baidubce.com:9091
  Topic: c42867cb52124ef4bfe51d315eea6f68__res_manager_online

# 套餐校验服务
CCEArtifactService:
  Image:
    ImageID: cce-artifact-service-IMAGEID # cce_stack_control.sh 会进行变量替换
  Config:
    HttpPort: 8795
    # 如流群 id
    ToID:
      - 10066881
    WebHookURL: http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dd23cd6cfebf8c1d40963fa6809c6db5b
    CronEnable: true
    MySQLEndpoint: cce_service_w:XjJPujGjSDb@tcp(mysql.cce-bd.baidu-int.com:5979)/bdbl_cce_center_db?charset=utf8&parseTime=true
    Users:
      - zhuangruiqing
      - v_yuanwentao
